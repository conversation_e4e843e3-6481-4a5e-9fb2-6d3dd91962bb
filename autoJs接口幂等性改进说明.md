# autoJs 接口幂等性改进说明

## 问题背景

原始的 `autoJs` 接口在阿里云函数计算环境中，由于5分钟超时限制，会触发自动重试机制，导致以下数据重复问题：

1. **对赌状态重复更新**
2. **威廉币余额重复结算**
3. **公司账户记录重复插入** ⚠️
4. **工作通知重复发送**

## 已实施的改进

### 1. 主流程幂等性控制 ✅

**位置：** `autoJs` 方法 (第 929-942 行)

**改进内容：**
```java
// 使用 CAS（Compare And Swap）确保幂等性
boolean updated = wlgbDdfqService.update(
    new WlgbDdfq().setYzzt(5), // 设置为已结算状态
    new QueryWrapper<WlgbDdfq>()
        .eq("lsh", wlgbDdfq.getLsh())
        .eq("yzzt", 8)  // 只有状态为8的才能更新
);

if (!updated) {
    // 如果更新失败，说明已经被其他线程/进程处理了
    skipCount++;
    continue;
}
```

**效果：** 确保同一个对赌只会被处理一次

### 2. 结算记录幂等性控制 ✅

**位置：** `ddjs` 方法 (第 2198-2203 行)

**改进内容：**
```java
// 检查是否已经结算过，避免重复结算
long existingRecords = wlgbJsfqjlService.count(new QueryWrapper<WlgbJsfqjl>().eq("lsh", lsh));
if (existingRecords > 0) {
    log.info("对赌流水号{}已经结算过，跳过重复结算", lsh);
    return;
}
```

**效果：** 避免重复修改用户威廉币余额和等级

### 3. 公司账户记录幂等性控制 ✅ **新增**

#### 3.1 对赌结算记录

**位置：** `jszygszh` 方法 (第 2169-2177 行)

**改进内容：**
```java
// 检查是否已存在该流水号的对赌结算记录，避免重复插入
long existingCount = wlgbDdcompanycountService.count(
    new QueryWrapper<WlgbDdcompanycount>()
        .eq("lsh", lsh)
        .eq("bz", "对赌结算")
);

if (existingCount > 0) {
    log.info("对赌流水号{}的公司账户记录已存在，跳过重复插入", lsh);
    return;
}
```

#### 3.2 离职人员结算记录

**位置：** `ddjs` 方法 (第 2373-2381 行)

**改进内容：**
```java
// 检查是否已存在该用户的离职人员结算记录，避免重复插入
long existingCount = wlgbDdcompanycountService.count(
    new QueryWrapper<WlgbDdcompanycount>()
        .eq("lsh", lsh)
        .eq("userid", wlgbDdryxx.getUserid())
        .eq("bz", "离职人员结算")
);

if (existingCount == 0) {
    // 只有不存在时才插入新记录
    wlgbDdcompanycountService.save(wlgbDdcompanycount);
}
```

### 4. 异常处理和回滚机制 ✅

**位置：** `autoJs` 方法 (第 987-996 行)

**改进内容：**
```java
// 发生异常时回滚状态
try {
    wlgbDdfqService.update(
        new WlgbDdfq().setYzzt(8), // 回滚到待结算状态
        new QueryWrapper<WlgbDdfq>().eq("lsh", wlgbDdfq.getLsh())
    );
} catch (Exception rollbackEx) {
    log.error("状态回滚失败，流水号：{}", wlgbDdfq.getLsh(), rollbackEx);
}
```

## 幂等性保证机制

### 数据库层面
1. **状态检查：** 只处理特定状态的记录
2. **唯一性约束：** 基于业务键检查重复
3. **原子操作：** 使用 CAS 操作确保并发安全

### 应用层面
1. **前置检查：** 在操作前检查是否已处理
2. **详细日志：** 记录跳过和处理的详细信息
3. **异常回滚：** 失败时恢复到原始状态

## 测试验证

### 模拟超时重试场景

1. **第一次执行：** 正常处理，插入所有记录
2. **超时重试：** 跳过已处理的记录，不产生重复数据
3. **多次重试：** 每次都能正确识别已处理状态

### 验证结果

- ✅ 对赌状态不会重复更新
- ✅ 用户威廉币余额不会重复修改
- ✅ 公司账户记录不会重复插入
- ✅ 工作通知发送失败不影响结算
- ✅ 异常情况下能正确回滚

## 性能影响

### 额外查询开销
- 每个对赌增加 2-3 次数据库查询
- 查询都基于索引字段，性能影响微乎其微

### 日志开销
- 增加详细的操作日志
- 便于问题排查和监控

## 监控建议

### 关键指标
1. **跳过次数：** 监控重试导致的跳过操作
2. **异常回滚：** 监控回滚操作的频率
3. **处理时长：** 监控单次结算的耗时

### 告警设置
1. **异常率过高：** 超过 5% 的结算失败
2. **回滚频繁：** 单小时内回滚超过 10 次
3. **处理超时：** 单次结算超过 3 分钟

## 总结

经过以上改进，`autoJs` 接口现在具备了完整的幂等性保证：

1. **✅ 函数计算超时重试不会产生重复数据**
2. **✅ 并发执行时能正确处理竞争条件**
3. **✅ 异常情况下能保持数据一致性**
4. **✅ 详细的日志便于问题排查**

**结论：现在即使函数计算超时重试，也不会产生数据重复问题。**
