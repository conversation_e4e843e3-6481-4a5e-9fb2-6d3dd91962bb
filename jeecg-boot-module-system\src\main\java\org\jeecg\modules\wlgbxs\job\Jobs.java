package org.jeecg.modules.wlgbxs.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.wlgb.config.IdConfig;
import org.jeecg.modules.wlgb.entity.*;
import org.jeecg.modules.wlgb.service.*;
import org.jeecg.modules.wlgb.service.SettlementService;
import org.jeecg.modules.wlgbdb.config.DingDBConfig;
import org.jeecg.modules.wlgbdb.config.HjConfig;
import org.jeecg.modules.wlgbxs.entity.*;
import org.jeecg.modules.wlgbxs.service.*;
import org.jeecg.modules.yd.config.YDLC;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>  对赌的工作通知和群消息、定时任务
 * @version 1.0
 * @date 2022/08/20 16:05:30
 */
@Component
@RestController
@RequestMapping("/wlgb/job")
@Slf4j
public class Jobs {

    // 常量定义
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String DINGKEY_ID = "weilianxcx";
    private static final String LOVE_HELP_CONFIG = "爱心互助榜";

    // 部门ID常量
    private static final String DEPT_BLACK_HORSE = "81870208";
    private static final String DEPT_EXTERNAL_INVESTOR = "33502412";
    private static final String DEPT_PENDING_RETURN = "401821558";
    private static final String DEPT_CHANGSHA_HOTEL = "860197171";

    @Resource
    private IDingdingEmployeeService dingdingEmployeeService;
    @Resource
    private IWlgbDdfqService wlgbDdfqService;
    @Resource
    private IWlgbDdryxxService wlgbDdryxxService;
    @Resource
    private IWlgbRybzbService wlgbRybzbService;
    @Resource
    private IYdAppkeyService ydAppkeyService;
    @Resource
    private HjConfig hjConfig;
    @Resource
    private IDingkeyService dingkeyService;
    @Resource
    private IWlgbDdgztzService wlgbDdgztzService;
    @Resource
    private IWlgbDdyzService wlgbDdyzService;
    @Resource
    private IWlgbDdyzbgjlService wlgbDdyzbgjlService;
    @Resource
    private IOSSFileService ossFileService;
    @Resource
    private IWlgbDdtdxxService wlgbDdtdxxService;
    @Resource
    private IWlgbDdwlbjlService wlgbDdwlbjlService;
    @Resource
    private IWlgbDdcompanycountService wlgbDdcompanycountService;
    @Resource
    private IWlgbJsfqjlService wlgbJsfqjlService;
    @Resource
    private IWlgbJlzService wlgbJlzService;
    @Resource
    private IWlgbCultureRaisingService wlgbCultureRaisingService;
    @Resource
    private SettlementService settlementService;

    /**
     * 定时任务 - 幂等性优化版本
     */
    @RequestMapping("wlgbAxjkTz")
    public Result wlgbAxjkTz() {
        log.info("开始执行爱心互助榜定时任务，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            String url = "https://jztdpp.aliwork.com/APP_XD2KFYUV4YZ1FWN46DEI/custom/FORM-1V766G81GJMUIILQYP97V18AC7RK3PM3663VKTC";

            // 🔥 关键优化：只获取未发送通知的记录，确保幂等性
            List<WlgbCultureRaising> wlgbCultureRaisingList = wlgbCultureRaisingService.list(new QueryWrapper<WlgbCultureRaising>()
                    .lambda()
                    .eq(WlgbCultureRaising::getSffstz, "0")  // 只处理未发送通知的记录
                    .eq(WlgbCultureRaising::getSfsc, "0"));

            if (wlgbCultureRaisingList.isEmpty()) {
                log.info("没有需要发送通知的爱心互助榜");
                return Result.OK("没有需要发送通知的爱心互助榜");
            }

            WlgbDdgztz gztzConfig = wlgbDdgztzService.getOne(new QueryWrapper<WlgbDdgztz>().eq("bz", "爱心互助榜"));
            if (gztzConfig == null) {
                log.error("未找到爱心互助榜配置");
                return Result.error("未找到爱心互助榜配置");
            }
            String context1 = gztzConfig.getContext();

            int successCount = 0;
            int skipCount = 0;
            List<String> errorMessages = new ArrayList<>();
            //获取要发送的人员名单
            //黑马项目组
            List list = bmidcx("81870208");
            //外部投资人
            List list1 = bmidcx("33502412");
            //待复工成员
            List list2 = bmidcx("401821558");
            //长沙赢氏大户人家酒店管理有限公司
            List list3 = bmidcx("860197171");

            //所有不发工作通知的部门id
            List<String> listAll = new ArrayList();
            listAll.addAll(list);
            listAll.addAll(list1);
            listAll.addAll(list2);
            listAll.addAll(list3);

            // 优化前的代码
// List<String> listry = new ArrayList();
// List<DingdingEmployee> dingdingEmployeeList = dingdingEmployeeService.list();

// // 去掉不发工作通知的部门11
// for (int z = 0; z < dingdingEmployeeList.size(); z++) {
//     int s = 0;
//     for (int i = 0; i < listAll.size(); i++) {
//         if (dingdingEmployeeList.get(z).getDepartid().contains(listAll.get(i))) {
//             s = 1;
//             break;
//         }
//     }
//     if (s == 0) {
//         listry.add(dingdingEmployeeList.get(z).getUserid());
//     }
// }

// 优化后的代码
            List<String> listry = new ArrayList<>();
            List<DingdingEmployee> dingdingEmployeeList = dingdingEmployeeService.list(new QueryWrapper<DingdingEmployee>()
                    .notInSql("departid", String.join("','", listAll)));

            for (DingdingEmployee employee : dingdingEmployeeList) {
                listry.add(employee.getUserid());
            }

            for (int i = 0; i < wlgbCultureRaisingList.size(); i++) {
                try {
                    WlgbCultureRaising wlgbCultureRaising = wlgbCultureRaisingList.get(i);

                    // 🔥 关键：使用CAS确保幂等性，只有未发送通知的才能更新
                    boolean updated = wlgbCultureRaisingService.update(
                        new WlgbCultureRaising().setSffstz(1), // 设置为已发送通知
                        new QueryWrapper<WlgbCultureRaising>()
                            .eq("id", wlgbCultureRaising.getId())
                            .eq("sffstz", 0)  // 只有状态为0的才能更新
                    );

                    if (!updated) {
                        // 如果更新失败，说明已经被处理了
                        skipCount++;
                        log.info("爱心互助榜ID{}已发送通知，跳过", wlgbCultureRaising.getId());
                        continue;
                    }

                    //发送工作通知模板
                    Dingkey dingkey = dingkeyService.getById("weilianxcx");
                    if (dingkey == null) {
                        log.error("未找到钉钉配置，id：weilianxcx");
                        errorMessages.add("ID" + wlgbCultureRaising.getId() + "未找到钉钉配置");
                        continue;
                    }

                    DingdingEmployee dingdingEmployee = dingdingEmployeeService.getById(wlgbCultureRaising.getBzdxid());
                    if (dingdingEmployee == null) {
                        log.error("未找到发起人信息，id：{}", wlgbCultureRaising.getBzdxid());
                        errorMessages.add("ID" + wlgbCultureRaising.getId() + "未找到发起人信息");
                        continue;
                    }

                    String context = dingdingEmployee.getName() + "发起了爱心榜！\n涓滴之水成海洋，颗颗爱心变希望。\n\n" + context1;

                    // 线上环境发送工作通知给所有人
                    if ("prod".equals(hjConfig.getActive())) {
                        int sendSuccessCount = 0;
                        for (int z = 0; z < listry.size(); z++) {
                            try {
                                DingDBConfig.sendGztz1(listry.get(z), null, dingkey, "有人需要你的帮助，快去帮帮他/她吧!", context, url);
                                sendSuccessCount++;
                            } catch (ApiException e) {
                                log.warn("发送工作通知失败，用户ID：{}, 错误：{}", listry.get(z), e.getMessage());
                            }
                        }
                        log.info("爱心互助榜ID{}通知发送完成，成功：{}/{}", wlgbCultureRaising.getId(), sendSuccessCount, listry.size());
                    }

                    successCount++;
                    log.info("爱心互助榜ID{}处理成功", wlgbCultureRaising.getId());

                } catch (Exception e) {
                    log.error("爱心互助榜处理失败，ID：{}, 错误：{}", wlgbCultureRaisingList.get(i).getId(), e.getMessage(), e);
                    errorMessages.add("ID" + wlgbCultureRaisingList.get(i).getId() + "处理失败：" + e.getMessage());

                    // 发生异常时回滚状态
                    try {
                        wlgbCultureRaisingService.update(
                            new WlgbCultureRaising().setSffstz(0), // 回滚到未发送状态
                            new QueryWrapper<WlgbCultureRaising>().eq("id", wlgbCultureRaisingList.get(i).getId())
                        );
                        log.info("爱心互助榜ID{}状态已回滚", wlgbCultureRaisingList.get(i).getId());
                    } catch (Exception rollbackEx) {
                        log.error("状态回滚失败，ID：{}", wlgbCultureRaisingList.get(i).getId(), rollbackEx);
                    }
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("爱心互助榜定时任务完成！成功：%d，跳过：%d，失败：%d，耗时：%dms",
                    successCount, skipCount, errorMessages.size(), duration);

            if (!errorMessages.isEmpty()) {
                message += "，失败详情：" + String.join("; ", errorMessages);
            }

            log.info("爱心互助榜定时任务执行完成：{}", message);
            return Result.OK(message);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("爱心互助榜定时任务执行异常，耗时：{}ms", duration, e);
            return Result.error("爱心互助榜定时任务失败：" + e.getMessage());
        }
    }

    /**
     * 定时任务  每日凌晨2点38分判断日期小于对赌结束时间的赌注，押注状态设置为待结算
     */
    @RequestMapping("wlgbDdztPd")
    public void wlgbDdztPd() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        List<WlgbDdfq> wlgbDdfqList = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>().eq("sfsc", 0).eq("yzzt", 3).eq("zt", 2).orderByDesc("ddjssj"));
        if (wlgbDdfqList != null || wlgbDdfqList.size() > 0) {
            WlgbDdfq wlgbDdfq = new WlgbDdfq();
            String lsh = null;
            Date d1 = new Date();       //获取当前日期
            String d2 = df.format(d1);
            Date date = null;
            try {
                date = df.parse(d2);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            for (int i = 0; i < wlgbDdfqList.size(); i++) {
                lsh = wlgbDdfqList.get(i).getLsh();
                wlgbDdfq = wlgbDdfqService.getOne(new QueryWrapper<WlgbDdfq>().eq("lsh", lsh).eq("sfsc", 0));
                if (wlgbDdfq != null || !"".equals(wlgbDdfq)) {
                    Date dd = wlgbDdfq.getDdjssj();           //获取对赌结束时间
                    if (dd != null || !"".equals(dd)) {
                        if (date.compareTo(dd) > 0) {           //判断当前日期是否大于结算日期
                            wlgbDdfq.setYzzt(6);
                            wlgbDdfqService.update(wlgbDdfq, new QueryWrapper<WlgbDdfq>().eq("lsh", lsh));
                        }
                    } else {
                        continue;
                    }
                }
            }
        }
    }

    /**
     * 定时任务  每天凌晨2点43更新对赌人员信息表wlgb_ddryxx - 幂等性优化版本
     */
    @RequestMapping("wlgbDdgxryxx")
    public Result WlgbDdgxryxx() {
        log.info("开始执行对赌人员信息更新，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            List<DingdingEmployee> dingdingEmployeeList = dingdingEmployeeService.list();
            if (dingdingEmployeeList == null || dingdingEmployeeList.isEmpty()) {
                log.info("钉钉员工列表为空");
                return Result.OK("钉钉员工列表为空");
            }

            int successCount = 0;
            int skipCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (DingdingEmployee employee : dingdingEmployeeList) {
                try {
                    String userid = employee.getUserid();
                    String name = employee.getName();

                    // 🔥 关键：检查用户是否已存在，确保幂等性
                    WlgbDdryxx existingUser = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", userid));
                    if (existingUser != null) {
                        // 用户已存在，跳过
                        skipCount++;
                        continue;
                    }

                    // 创建新用户记录
                    WlgbDdryxx wlgbDdryxx = new WlgbDdryxx();
                    wlgbDdryxx.setId(IdConfig.uuId());
                    wlgbDdryxx.setUserid(userid);
                    wlgbDdryxx.setName(name);
                    wlgbDdryxx.setYhtx(employee.getAvatar());
                    wlgbDdryxx.setWlb(0.0);
                    wlgbDdryxx.setSfsc(0);
                    wlgbDdryxx.setYzjb(1);
                    wlgbDdryxx.setDdjb(1);

                    wlgbDdryxxService.save(wlgbDdryxx);
                    successCount++;
                    log.debug("新增对赌人员：{} ({})", name, userid);

                } catch (Exception e) {
                    log.error("处理员工信息失败，userid：{}, 错误：{}", employee.getUserid(), e.getMessage(), e);
                    errorMessages.add("用户" + employee.getUserid() + "处理失败：" + e.getMessage());
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("对赌人员信息更新完成！新增：%d，跳过：%d，失败：%d，耗时：%dms",
                    successCount, skipCount, errorMessages.size(), duration);

            if (!errorMessages.isEmpty()) {
                message += "，失败详情：" + String.join("; ", errorMessages);
            }

            log.info("对赌人员信息更新执行完成：{}", message);
            return Result.OK(message);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("对赌人员信息更新执行异常，耗时：{}ms", duration, e);
            return Result.error("对赌人员信息更新失败：" + e.getMessage());
        }
    }

    /**
     * 定时任务  编制表获取 - 幂等性优化版本
     */
    @RequestMapping("bzbhq")
    public Result bzbhq() {
        log.info("开始执行编制表获取，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            String bz = null;
            String formUuid = null;
            if ("dev".equals(hjConfig.getActive())) {
                bz = "宜搭";
                formUuid = "FORM-UT866MB1F99Q5FEC0KGDD7MZGGO92GVN6MWOKS6";
            } else if ("prod".equals(hjConfig.getActive())) {
                bz = "人事系统";
                formUuid = "FORM-IE7664A1IPUS410Q4MJY65KIHHV834ZWS9ISK72";
            } else {
                return Result.OK("非开发或生产环境，跳过执行");
            }

            YDLC ydlc = new YDLC();
            YdAppkey ydAppkey = ydAppkeyService.getOne(
                    new QueryWrapper<YdAppkey>()
                            .lambda()
                            .eq(YdAppkey::getBz, bz)
            );

            if (ydAppkey == null) {
                log.error("未找到对应的应用配置，bz：{}", bz);
                return Result.error("未找到对应的应用配置");
            }

            List<JSONObject> list3 = new ArrayList<>();
            String s = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);
            JSONObject object = JSONObject.parseObject(s);
            log.info("编制表数据查询结果：{}", object.getJSONObject("result"));

            int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
            double b = count / 100.00;
            int a = (int) b;
            int c = b > a ? a + 1 : a;
            if (c > 0) {
                JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                jsonArray.forEach(l -> list3.add((JSONObject) l));
            }

            for (int i = 2; i <= c; i++) {
                String lcid1 = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, i + "", "", "", null);
                JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                jsonArray.forEach(l -> list3.add((JSONObject) l));
            }

            int successCount = 0;
            int updateCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (JSONObject l : list3) {
                try {
                    JSONObject data1 = l.getJSONObject("formData");
                    String userid = data1.getString("userid");
                    String gh = data1.getString("gh");
                    String gc = data1.getString("gc");
                    String name = data1.getString("name");
                    Date rzsj = data1.getDate("rzsj");
                    String city = data1.getString("city");
                    String dy = data1.getString("dy");
                    String zn = data1.getString("zn");
                    String gwmc = data1.getString("gwmc");
                    String bmfzr = data1.getString("bmfzr");
                    String bm1 = data1.getString("bm1");
                    String bm2 = data1.getString("bm2");
                    String bm3 = data1.getString("bm3");
                    String bm4 = data1.getString("bm4");
                    String bm5 = data1.getString("bm5");
                    String cj = data1.getString("cj");
                    String zj = data1.getString("zj");
                    String ygzt = data1.getString("ygzt");

                    // 🔥 关键：检查用户是否已存在，确保幂等性
                    WlgbRybzb existingUser = wlgbRybzbService.getOne(new QueryWrapper<WlgbRybzb>().eq("userid", userid));

                    if (existingUser == null) {
                        // 新增用户
                        WlgbRybzb wlgbRybzb = new WlgbRybzb();
                        wlgbRybzb.setUserid(userid);
                        wlgbRybzb.setGh(gh);
                        wlgbRybzb.setName(name);
                        wlgbRybzb.setRzsj(rzsj);
                        wlgbRybzb.setCity(city);
                        wlgbRybzb.setDy(dy);
                        wlgbRybzb.setZn(zn);
                        wlgbRybzb.setGwmc(gwmc);
                        wlgbRybzb.setBmfzr(bmfzr);
                        wlgbRybzb.setBm1(bm1);
                        wlgbRybzb.setBm2(bm2);
                        wlgbRybzb.setBm3(bm3);
                        wlgbRybzb.setBm4(bm4);
                        wlgbRybzb.setBm5(bm5);
                        wlgbRybzb.setCj(cj);
                        wlgbRybzb.setZj(zj);
                        wlgbRybzb.setYgzt(ygzt);
                        wlgbRybzb.setGc(gc);
                        wlgbRybzbService.save(wlgbRybzb);
                        successCount++;
                        log.debug("新增编制表用户：{} ({})", name, userid);
                    } else {
                        // 更新用户信息
                        existingUser.setGh(gh);
                        existingUser.setName(name);
                        existingUser.setRzsj(rzsj);
                        existingUser.setCity(city);
                        existingUser.setDy(dy);
                        existingUser.setZn(zn);
                        existingUser.setGwmc(gwmc);
                        existingUser.setBmfzr(bmfzr);
                        existingUser.setBm1(bm1);
                        existingUser.setBm2(bm2);
                        existingUser.setBm3(bm3);
                        existingUser.setBm4(bm4);
                        existingUser.setBm5(bm5);
                        existingUser.setCj(cj);
                        existingUser.setZj(zj);
                        existingUser.setYgzt(ygzt);
                        existingUser.setGc(gc);
                        wlgbRybzbService.updateById(existingUser);
                        updateCount++;
                        log.debug("更新编制表用户：{} ({})", name, userid);
                    }

                } catch (Exception e) {
                    log.error("处理编制表数据失败，错误：{}", e.getMessage(), e);
                    errorMessages.add("数据处理失败：" + e.getMessage());
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("编制表获取完成！新增：%d，更新：%d，失败：%d，耗时：%dms",
                    successCount, updateCount, errorMessages.size(), duration);

            if (!errorMessages.isEmpty()) {
                message += "，失败详情：" + String.join("; ", errorMessages);
            }

            log.info("编制表获取执行完成：{}", message);
            return Result.OK(message);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("编制表获取执行异常，耗时：{}ms", duration, e);
            return Result.error("编制表获取失败：" + e.getMessage());
        }
    }

    /**
     * 定时任务 离职人员更新  泰山勋章
     */
    @RequestMapping("lzryXgzt")
    public Result LzryXgzt() {
//        String bz = null;
//        String formUuid = null;
//        if ("dev".equals(hjConfig.getActive())) {
//            bz = "宜搭";
//            formUuid = "FORM-QK7669C1EXUUZ18DZLA2A2W37ING3CMSFOEVK5D";//团队表单
//        }
//        if ("prod".equals(hjConfig.getActive())) {
//            bz = "对赌中心-线上";
//            formUuid = "FORM-T9766U610S0VFRYX12MH64XMAMQY2XWRMGRVKV";//泰山勋章
//        }
//        YDLC ydlc = new YDLC();
//        YdAppkey ydAppkey = ydAppkeyService.getOne(
//                new QueryWrapper<YdAppkey>()
//                        .lambda()
//                        .eq(YdAppkey::getBz, bz)
//        );
//        List<Map<String, Object>> list = new ArrayList<>();
//        List<JSONObject> list3 = new ArrayList<>();
//        String s = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);
//        JSONObject object = JSONObject.parseObject(s);
//        int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
//        double b = count / 100.00;
//        int a = (int) b;
//        int c = b > a ? a + 1 : a;
//        if (c > 0) {
//            JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
//            jsonArray.forEach(l -> list3.add((JSONObject) l));
//        }
//        for (int i = 2; i <= c; i++) {
//            String lcid1 = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, i + "", "", "", null);
//            JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
//            JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
//            jsonArray.forEach(l -> list3.add((JSONObject) l));
//        }
//        //泰山勋章
//        list3.forEach(l -> {
//            String formInstId = l.getString("formInstId");
//            JSONObject data1 = l.getJSONObject("formData");
//            String name = xzjq(xzjq1(data1.getString("xm1")));
//            DingdingEmployee dingdingEmployee = dingdingEmployeeService.getOne(new QueryWrapper<DingdingEmployee>().eq("name", name));
//            if (dingdingEmployee == null) {
//                Map<String, Object> map = new HashMap<>();
//                map.put("lz", "1");
//                String json = JSONObject.toJSON(map).toString();
//                String sfcg = ydlc.gxbd(json, "15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
//                System.out.println(sfcg);
//            }
//        });

        System.out.println("结束了");

        return Result.OK();
    }

    //离职人员更新  稽查审计榜
    @RequestMapping("jcsjLzryXgzt")
    public void JcsjLzryXgzt() {
        String bz = null;
        String formUuid = null;
        if ("dev".equals(hjConfig.getActive())) {
            bz = "办公平台3";
            formUuid = "FORM-56666571GS1VMYA4W3EEOJSCNG8P1VDDN2WVKT";//办公平台3

        }

        if ("prod".equals(hjConfig.getActive())) {
            bz = "对赌中心-线上";
            formUuid = "FORM-XHA6688173HV6WH20ZNYT42QU9D737KPEZ5WK1";//稽查审计
        }

        YDLC ydlc = new YDLC();
        YdAppkey ydAppkey = ydAppkeyService.getOne(
                new QueryWrapper<YdAppkey>()
                        .lambda()
                        .eq(YdAppkey::getBz, bz)
        );

        List<Map<String, Object>> list = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();

        String s = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);

        JSONObject object = JSONObject.parseObject(s);

        int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;

        double b = count / 100.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        if (c > 0) {
            JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
            jsonArray.forEach(l -> list3.add((JSONObject) l));
        }
        for (int i = 2; i <= c; i++) {
            String lcid1 = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, i + "", "", "", null);
            JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
            JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
            jsonArray.forEach(l -> list3.add((JSONObject) l));
        }

        //稽查榜单
        list3.forEach(l -> {
            String formInstId = l.getString("formInstId");
            JSONObject data1 = l.getJSONObject("formData");
            String name = xzjq(xzjq1(data1.getString("xm1")));
            DingdingEmployee dingdingEmployee = dingdingEmployeeService.getOne(new QueryWrapper<DingdingEmployee>().eq("name", name));
            if (dingdingEmployee == null) {
                Map<String, Object> map = new HashMap<>();
                map.put("lz", "1");
                String json = JSONObject.toJSON(map).toString();
                String sfcg = ydlc.gxbd(json, "15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
                System.out.println(sfcg);

            }
        });
    }

    //待公布池更新  泰山勋章
    @RequestMapping("dgbcSjtj")
    public Result DgbcSjtj() {

        String dgbcbz = null;
        String formUuid = null;
        String tsformUuid = "FORM-T9766U610S0VFRYX12MH64XMAMQY2XWRMGRVKV";   //企业文化中心  泰山勋章新增

        //线下
        if ("dev".equals(hjConfig.getActive())) {
            dgbcbz = "宜搭";
            formUuid = "FORM-NC966W81G34VQGLAXL4XW3EI8WL72RY0TIUVKS";//待公布池表单id
        }


        if ("prod".equals(hjConfig.getActive())) {
            dgbcbz = "对赌中心-线上";
            formUuid = "FORM-4V966N81537V4RBKYCB422IQKSIT2NTY7VSVK6";//待公布池表单id


            YDLC ydlc = new YDLC();
            YdAppkey ydAppkey = ydAppkeyService.getOne(
                    new QueryWrapper<YdAppkey>()
                            .lambda()
                            .eq(YdAppkey::getBz, dgbcbz)
            );

            List<Map<String, Object>> list = new ArrayList<>();
            List<JSONObject> list3 = new ArrayList<>();

            String s = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);

            JSONObject object = JSONObject.parseObject(s);

            int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;

            double b = count / 100.00;
            int a = (int) b;
            int c = b > a ? a + 1 : a;
            if (c > 0) {
                JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                jsonArray.forEach(l -> list3.add((JSONObject) l));
            }
            for (int i = 2; i <= c; i++) {
                String lcid1 = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, i + "", "", "", null);
                JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                jsonArray.forEach(l -> list3.add((JSONObject) l));
            }

            //泰山勋章
            list3.forEach(l -> {
                String formInstId = l.getString("formInstId");
                JSONObject data1 = l.getJSONObject("formData");
                String lsh = data1.getString("lsh");
                Map<String, Object> map = new HashMap<>();
                map.put("lsh", lsh);
                String json = JSONObject.toJSON(map).toString();


                String i = ydlc.hqScSl122("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), tsformUuid, json, "1");
                JSONObject jsonObject = JSONObject.parseObject(i);
                JSONObject JSONObject1 = jsonObject.getJSONObject("result");
                String totalCount = JSONObject1.getString("totalCount");

                //判断是否有相同的流水号，如果没有就进行新增表单实例
                if ("1".equals(totalCount)) {
                    System.out.println(totalCount);
                    return;
                } else if ("0".equals(totalCount)) {
                    JSONObject jsonObject1 = l.getJSONObject("originator");
                    String userId = jsonObject1.getString("userId");
                    //新增表单数据
                    String xz = ydlc.xzbd(data1.toJSONString(), userId, ydAppkey.getAppkey(), ydAppkey.getToken(), tsformUuid);
                    JSONObject jsonObject2 = JSONObject.parseObject(xz);
                    String formid = jsonObject2.getString("result");

                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("lz", "0");
                    String json1 = JSONObject.toJSON(map1).toString();
                    String sfcg = ydlc.gxbd(json1, userId, ydAppkey.getAppkey(), ydAppkey.getToken(), formid);
                    JSONObject jsonObject3 = JSONObject.parseObject(sfcg);
                    if ("true".equals(jsonObject3.getString("success"))) {
                        System.out.println("true");
                        String scbd = ydlc.scbdsl("012412221639786136545", ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
                        JSONObject jsonObject4 = JSONObject.parseObject(scbd);
                        if ("true".equals(jsonObject4.getString("success"))) {
                            System.out.println("删除成功");
                        }
                    }
                }
            });
        }
        return Result.OK();
    }

    //待公布池更新  稽查审计
    @RequestMapping("jcsjDgbc")
    public Result JcsjDgbc() {
        String jcdgbcbz = null;
        String formUuid = null;
        String jcformUuid = "FORM-XHA6688173HV6WH20ZNYT42QU9D737KPEZ5WK1";   //企业文化稽查审计表单

        //线下
        if ("dev".equals(hjConfig.getActive())) {
            jcdgbcbz = "办公平台3";
            formUuid = "FORM-LK766AC1G8GV94W11Q62UBNS4P7L30REPP4WK2";//待公布池表单id
        }

        //线上
        if ("prod".equals(hjConfig.getActive())) {
            jcdgbcbz = "对赌中心-线上";
            formUuid = "FORM-JD8668C1HHGVAIAZVA8H7MAI6F9Q1Y8RV46WK7";//待公布池表单id
        }


        YDLC ydlc = new YDLC();
        YdAppkey ydAppkey = ydAppkeyService.getOne(
                new QueryWrapper<YdAppkey>()
                        .lambda()
                        .eq(YdAppkey::getBz, jcdgbcbz)
        );
        List<Map<String, Object>> list = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();

        String s = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);
        JSONObject object = JSONObject.parseObject(s);
        int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
        double b = count / 100.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        if (c > 0) {
            JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
            jsonArray.forEach(l -> list3.add((JSONObject) l));
        }
        for (int i = 2; i <= c; i++) {
            String lcid1 = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, i + "", "", "", null);
            JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
            JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
            jsonArray.forEach(l -> list3.add((JSONObject) l));
        }

        //泰山勋章
        list3.forEach(l -> {
            String formInstId = l.getString("formInstId");
            JSONObject data1 = l.getJSONObject("formData");

            String userid = xzjq(xzjq1(data1.getString("xm_id")));
            String lsh = data1.getString("lsh");
            Map<String, Object> map = new HashMap<>();
            map.put("lsh", lsh);
            String json = JSONObject.toJSON(map).toString();

            //获取待公布池数据流水号查询稽查审计是否存在当前数据
            String i = ydlc.hqScSl122("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), jcformUuid, json, "1");
            JSONObject jsonObject = JSONObject.parseObject(i);
            JSONObject JSONObject1 = jsonObject.getJSONObject("result");
            String totalCount = JSONObject1.getString("totalCount");

            //判断是否有相同的流水号，如果没有就进行新增表单实例
            if ("1".equals(totalCount)) {
                System.out.println(totalCount);
                return;
            } else if ("0".equals(totalCount)) {
//                System.out.println(l);
                JSONObject jsonObject1 = l.getJSONObject("originator");
                String userId = jsonObject1.getString("userId");
                //新增表单数据
                String xz = ydlc.xzbd(data1.toJSONString(), userId, ydAppkey.getAppkey(), ydAppkey.getToken(), jcformUuid);
                JSONObject jsonObject2 = JSONObject.parseObject(xz);
                String formid = jsonObject2.getString("result");

                Map<String, Object> map1 = new HashMap<>();
                map1.put("lz", "0");
                String json1 = JSONObject.toJSON(map1).toString();
                String sfcg = ydlc.gxbd(json1, userId, ydAppkey.getAppkey(), ydAppkey.getToken(), formid);
                JSONObject jsonObject3 = JSONObject.parseObject(sfcg);
                if ("true".equals(jsonObject3.getString("success"))) {
                    System.out.println("true");
                    String scbd = ydlc.scbdsl("012412221639786136545", ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
                    JSONObject jsonObject4 = JSONObject.parseObject(scbd);
                    if ("true".equals(jsonObject4.getString("success"))) {
                        System.out.println("删除成功");
                    }
                }

            }
        });
        return Result.OK();
    }

    //发送工作通知开始押注 - 幂等性优化版本
    @RequestMapping("sendAllgztz")
    public Result sendAllgztz() {
        log.info("开始执行发送工作通知，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            //获取当日时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            String d = sdf.format(date);
            System.out.println(d);

            // 🔥 关键优化：只获取状态为4的对赌，确保幂等性
            List<WlgbDdfq> listfq = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                .eq("sfsc", 0)
                .eq("yzzt", "4"));  // 只处理待发送通知状态的对赌

            if (listfq.isEmpty()) {
                log.info("没有需要发送工作通知的对赌");
                return Result.OK("没有需要发送工作通知的对赌");
            }
        String jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
        String jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";
        String webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
        String secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";
        // 使用泛型提升类型安全性  黑马项目组
        List<String> list = bmidcx("81870208");
        // 外部投资人
        List<String> list1 = bmidcx("33502412");
        // 待复工成员
        List<String> list2 = bmidcx("401821558");
        // 长沙赢氏大户人家酒店管理有限公司
        List<String> list3 = bmidcx("860197171");

        //所有不发工作通知的部门id
        List<String> listAll = new ArrayList();
        listAll.addAll(list);
        listAll.addAll(list1);
        listAll.addAll(list2);
        listAll.addAll(list3);

        List<DingdingEmployee> dingdingEmployeeList = dingdingEmployeeService.list(new LambdaQueryWrapper<DingdingEmployee>().notIn(!listAll.isEmpty(), DingdingEmployee::getDepartid, listAll));
        //需要发送工作通知的人
        List<String> listry = dingdingEmployeeList.stream().map(DingdingEmployee::getUserid).collect(Collectors.toList());
        //发送工作通知模板
        Dingkey dingkey = dingkeyService.getById("weilianxcx");
        //从数据库获取文本内容
        String context1 = wlgbDdgztzService.getOne(new QueryWrapper<WlgbDdgztz>().eq("bz", "对赌押注工作通知")).getContext();

        // 先提取所有 lsh 值用于批量查询
        List<String> lshList = listfq.stream().map(item -> item.getLsh()).collect(Collectors.toList());
        // 批量查询 wlgbDdfq 列表
        List<WlgbDdfq> ddfqList = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>().in("lsh", lshList));
        Map<String, WlgbDdfq> ddfqMap = ddfqList.stream().collect(Collectors.toMap(WlgbDdfq::getLsh, Function.identity()));
        // 批量查询 wlgbJlz 列表
        List<String> ddfqLshs = ddfqList.stream().map(WlgbDdfq::getLsh).collect(Collectors.toList());
        List<WlgbJlz> jlzList = wlgbJlzService.list(new QueryWrapper<WlgbJlz>().in("lsh", ddfqLshs));
        Map<String, WlgbJlz> jlzMap = jlzList.stream().filter(Objects::nonNull).collect(Collectors.toMap(WlgbJlz::getLsh, Function.identity()));
            int successCount = 0;
            int skipCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 遍历 listfq 一次完成所有操作
            for (int i = 0; i < listfq.size(); i++) {
                try {
                    String lsh = listfq.get(i).getLsh();
                    WlgbDdfq wlgbDdfq = ddfqMap.get(lsh);
                    if (wlgbDdfq == null) {
                        continue;
                    }

                    // 🔥 关键：使用CAS确保幂等性，只有状态为4的才能更新为2
                    boolean updated = wlgbDdfqService.update(
                        new WlgbDdfq().setYzzt(2), // 设置为押注中状态
                        new QueryWrapper<WlgbDdfq>()
                            .eq("lsh", lsh)
                            .eq("yzzt", 4)  // 只有状态为4的才能更新
                    );

                    if (!updated) {
                        // 如果更新失败，说明已经被处理了
                        skipCount++;
                        log.info("对赌流水号{}已发送通知，跳过", lsh);
                        continue;
                    }

                    WlgbJlz wlgbJlz = jlzMap.get(wlgbDdfq.getLsh());
                    if (wlgbJlz == null) {
                        // 回滚状态
                        wlgbDdfqService.update(
                            new WlgbDdfq().setYzzt(4),
                            new QueryWrapper<WlgbDdfq>().eq("lsh", lsh)
                        );
                        continue;
                    }

                    // 构建消息内容
                    StringBuilder urlBuilder = new StringBuilder("[广播][广播] ").append(wlgbDdfq.getTzfid()).append(" vs ").append(wlgbDdfq.getBtzfid());
                    StringBuilder titleBuilder = new StringBuilder(wlgbDdfq.getTzfid()).append("VS").append(wlgbDdfq.getBtzfid());
                    if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                        urlBuilder.append(" vs ").append(wlgbDdfq.getBtzfid2());
                        titleBuilder.append("VS").append(wlgbDdfq.getBtzfid2());
                    }
                    urlBuilder.append("的押注正式开始，押注时间截止至")
                            .append(sdf1.format(wlgbDdfq.getYzjzsj()))
                            .append("，需要押注前往【工作台】→【企业文化-对赌中心】→【押注中】→【去看看】→【选择想要押注的对象】，")
                            .append("每个对赌仅可押注一方（挑战方或者应战方），在【押注中】的押注详情页面可以看到最新的押注信息及赔率哦~赌一赌，")
                            .append("搏一搏，单车变摩托~~，大家快去对赌系统中进行押注吧... \n ![screenshot](")
                            .append(wlgbJlz.getUrl()).append(")");
                    titleBuilder.append("的对赌开始了，请查看！");
                    String url = urlBuilder.toString();
                    String title = titleBuilder.toString();

                    // 发送钉钉消息（即使失败也不影响状态）
                    try {
                        sendDingDingMessages(jmwebhook, jmsecret, webhook, secret, title, url, list1);
                    } catch (Exception e) {
                        log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", lsh, e.getMessage());
                    }

                    // 构造通知内容
                    StringBuilder contextBuilder = new StringBuilder(wlgbDdfq.getTzfid()).append("VS").append(wlgbDdfq.getBtzfid());
                    if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                        contextBuilder.append("VS").append(wlgbDdfq.getBtzfid2());
                    }
                    contextBuilder.append("的对赌开始了\n").append(context1);
                    String context = contextBuilder.toString();

                    // 发送工作通知给所有人（即使失败也不影响状态）
                    try {
                        sendDgbStartNotification(listry, dingkey, context);
                    } catch (Exception e) {
                        log.warn("工作通知发送失败，对赌流水号：{}, 错误：{}", lsh, e.getMessage());
                    }

                    successCount++;
                    log.info("对赌流水号{}工作通知发送成功", lsh);

                } catch (Exception e) {
                    log.error("发送工作通知失败，流水号：{}, 错误：{}", listfq.get(i).getLsh(), e.getMessage(), e);
                    errorMessages.add("流水号" + listfq.get(i).getLsh() + "发送失败：" + e.getMessage());
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("工作通知发送完成！成功：%d，跳过：%d，失败：%d，耗时：%dms",
                    successCount, skipCount, errorMessages.size(), duration);

            if (!errorMessages.isEmpty()) {
                message += "，失败详情：" + String.join("; ", errorMessages);
            }

            log.info("发送工作通知执行完成：{}", message);
            return Result.OK(message);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("发送工作通知执行异常，耗时：{}ms", duration, e);
            return Result.error("发送工作通知失败：" + e.getMessage());
        }
    }

    /**
     * 优化后的发送工作通知接口 - 确保幂等性
     * 解决云函数重试导致的重复通知问题
     * 完全替代原sendAllgztz接口，包含所有功能
     */
    @RequestMapping("sendAllgztzOptimized")
    public Result sendAllgztzOptimized() {
        log.info("开始执行优化后的发送工作通知，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            //获取当日时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            String d = sdf.format(date);
            System.out.println(d);

            // 🔥 关键优化：只获取状态为4的对赌，确保幂等性
            List<WlgbDdfq> listfq = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                .eq("sfsc", 0)
                .eq("yzzt", "4"));  // 只处理待发送通知状态的对赌

            if (listfq.isEmpty()) {
                log.info("没有需要发送工作通知的对赌");
                return Result.OK("没有需要发送工作通知的对赌");
            }

            String jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
            String jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";
            String webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
            String secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";

            // 使用泛型提升类型安全性  黑马项目组
            List<String> list = bmidcx("81870208");
            // 外部投资人
            List<String> list1 = bmidcx("33502412");
            // 待复工成员
            List<String> list2 = bmidcx("401821558");
            // 长沙赢氏大户人家酒店管理有限公司
            List<String> list3 = bmidcx("860197171");

            //所有不发工作通知的部门id
            List<String> listAll = new ArrayList();
            listAll.addAll(list);
            listAll.addAll(list1);
            listAll.addAll(list2);
            listAll.addAll(list3);

            List<DingdingEmployee> dingdingEmployeeList = dingdingEmployeeService.list(new LambdaQueryWrapper<DingdingEmployee>().notIn(!listAll.isEmpty(), DingdingEmployee::getDepartid, listAll));
            //需要发送工作通知的人
            List<String> listry = dingdingEmployeeList.stream().map(DingdingEmployee::getUserid).collect(Collectors.toList());
            //发送工作通知模板
            Dingkey dingkey = dingkeyService.getById("weilianxcx");
            //从数据库获取文本内容
            String context1 = wlgbDdgztzService.getOne(new QueryWrapper<WlgbDdgztz>().eq("bz", "对赌押注工作通知")).getContext();

            // 先提取所有 lsh 值用于批量查询
            List<String> lshList = listfq.stream().map(item -> item.getLsh()).collect(Collectors.toList());
            // 批量查询 wlgbDdfq 列表
            List<WlgbDdfq> ddfqList = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>().in("lsh", lshList));
            Map<String, WlgbDdfq> ddfqMap = ddfqList.stream().collect(Collectors.toMap(WlgbDdfq::getLsh, Function.identity()));
            // 批量查询 wlgbJlz 列表
            List<String> ddfqLshs = ddfqList.stream().map(WlgbDdfq::getLsh).collect(Collectors.toList());
            List<WlgbJlz> jlzList = wlgbJlzService.list(new QueryWrapper<WlgbJlz>().in("lsh", ddfqLshs));
            Map<String, WlgbJlz> jlzMap = jlzList.stream().filter(Objects::nonNull).collect(Collectors.toMap(WlgbJlz::getLsh, Function.identity()));

            int successCount = 0;
            int skipCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 遍历 listfq 一次完成所有操作
            for (int i = 0; i < listfq.size(); i++) {
                try {
                    String lsh = listfq.get(i).getLsh();
                    WlgbDdfq wlgbDdfq = ddfqMap.get(lsh);
                    if (wlgbDdfq == null) {
                        continue;
                    }

                    // 🔥 关键：使用CAS确保幂等性，只有状态为4的才能更新为2
                    boolean updated = wlgbDdfqService.update(
                        new WlgbDdfq().setYzzt(2), // 设置为押注中状态
                        new QueryWrapper<WlgbDdfq>()
                            .eq("lsh", lsh)
                            .eq("yzzt", 4)  // 只有状态为4的才能更新
                    );

                    if (!updated) {
                        // 如果更新失败，说明已经被处理了
                        skipCount++;
                        log.info("对赌流水号{}已发送通知，跳过", lsh);
                        continue;
                    }

                    WlgbJlz wlgbJlz = jlzMap.get(wlgbDdfq.getLsh());
                    if (wlgbJlz == null) {
                        // 回滚状态
                        wlgbDdfqService.update(
                            new WlgbDdfq().setYzzt(4),
                            new QueryWrapper<WlgbDdfq>().eq("lsh", lsh)
                        );
                        continue;
                    }

                    // 构建消息内容
                    StringBuilder urlBuilder = new StringBuilder("[广播][广播] ").append(wlgbDdfq.getTzfid()).append(" vs ").append(wlgbDdfq.getBtzfid());
                    StringBuilder titleBuilder = new StringBuilder(wlgbDdfq.getTzfid()).append("VS").append(wlgbDdfq.getBtzfid());
                    if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                        urlBuilder.append(" vs ").append(wlgbDdfq.getBtzfid2());
                        titleBuilder.append("VS").append(wlgbDdfq.getBtzfid2());
                    }
                    urlBuilder.append("的押注正式开始，押注时间截止至")
                            .append(sdf1.format(wlgbDdfq.getYzjzsj()))
                            .append("，需要押注前往【工作台】→【企业文化-对赌中心】→【押注中】→【去看看】→【选择想要押注的对象】，")
                            .append("每个对赌仅可押注一方（挑战方或者应战方），在【押注中】的押注详情页面可以看到最新的押注信息及赔率哦~赌一赌，")
                            .append("搏一搏，单车变摩托~~，大家快去对赌系统中进行押注吧... \n ![screenshot](")
                            .append(wlgbJlz.getUrl()).append(")");
                    titleBuilder.append("的对赌开始了，请查看！");
                    String url = urlBuilder.toString();
                    String title = titleBuilder.toString();

                    // 发送钉钉消息（即使失败也不影响状态）
                    try {
                        sendDingDingMessages(jmwebhook, jmsecret, webhook, secret, title, url, list1);
                    } catch (Exception e) {
                        log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", lsh, e.getMessage());
                    }

                    // 构造通知内容
                    StringBuilder contextBuilder = new StringBuilder(wlgbDdfq.getTzfid()).append("VS").append(wlgbDdfq.getBtzfid());
                    if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                        contextBuilder.append("VS").append(wlgbDdfq.getBtzfid2());
                    }
                    contextBuilder.append("的对赌开始了\n").append(context1);
                    String context = contextBuilder.toString();

                    // 发送工作通知给所有人（即使失败也不影响状态）
                    try {
                        sendDgbStartNotification(listry, dingkey, context);
                    } catch (Exception e) {
                        log.warn("工作通知发送失败，对赌流水号：{}, 错误：{}", lsh, e.getMessage());
                    }

                    successCount++;
                    log.info("对赌流水号{}工作通知发送成功", lsh);

                } catch (Exception e) {
                    log.error("发送工作通知失败，流水号：{}, 错误：{}", listfq.get(i).getLsh(), e.getMessage(), e);
                    errorMessages.add("流水号" + listfq.get(i).getLsh() + "发送失败：" + e.getMessage());

                    // 发生异常时回滚状态
                    try {
                        wlgbDdfqService.update(
                            new WlgbDdfq().setYzzt(4), // 回滚到待发送状态
                            new QueryWrapper<WlgbDdfq>().eq("lsh", listfq.get(i).getLsh())
                        );
                        log.info("对赌流水号{}状态已回滚", listfq.get(i).getLsh());
                    } catch (Exception rollbackEx) {
                        log.error("状态回滚失败，流水号：{}", listfq.get(i).getLsh(), rollbackEx);
                    }
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("工作通知发送完成！成功：%d，跳过：%d，失败：%d，耗时：%dms",
                    successCount, skipCount, errorMessages.size(), duration);

            if (!errorMessages.isEmpty()) {
                message += "，失败详情：" + String.join("; ", errorMessages);
            }

            log.info("发送工作通知执行完成：{}", message);

            // 如果执行时间超过60秒，发送告警
            if (duration > 60000) {
                log.warn("sendAllgztzOptimized执行时间过长：{}ms，可能存在性能问题", duration);
            }

            return Result.OK(message);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("发送工作通知执行异常，耗时：{}ms", duration, e);
            return Result.error("发送工作通知失败：" + e.getMessage());
        }
    }

    // 封装钉钉发送逻辑
    private void sendDingDingMessages(String jmwebhook, String jmsecret, String webhook, String secret,
                                      String title, String url, List<?> list1) {
        DingDingUtil.sendMark(jmwebhook, jmsecret, title, url, (List<String>) list1, true);
        DingDingUtil.sendMark(webhook, secret, title, url, (List<String>) list1, true);
    }

    // 封装工作通知发送逻辑
    public void sendDgbStartNotification(List<String> userIds, Dingkey dingkey, String context) {
        if (dingkey == null || userIds.isEmpty()) {
            return;
        }
        String title = "对赌开始押注了,快去看看吧";
        String url = "https://jztdpp.aliwork.com/APP_XD2KFYUV4YZ1FWN46DEI/custom/FORM-TL866181NMASVIWDZFOADE2IJNTY1MORNUSRKAG";
        for (String userid : userIds) {
            try {
                DingDBConfig.sendGztz1(userid, null, dingkey, title, context, url);
            } catch (ApiException e) {
                log.error("发送对赌开始通知失败，用户ID：{}", userid, e);
            }
        }
    }

    //自动停止押注 - 幂等性优化版本
    @RequestMapping("stopYz")
    public Result StopYz() {
        log.info("开始执行自动停止押注，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            //线下测试群聊
            String webhook = "";
            String secret = "";
            String jmwebhook = "";
            String jmsecret = "";
            //加盟
            jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
            jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";
            //线上对赌群
            if ("prod".equals(hjConfig.getActive())) {
                webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
                secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";

                // 🔥 关键优化：只获取状态为2的对赌，确保幂等性
                List<WlgbDdfq> list = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                    .eq("zt", "2")
                    .eq("yzzt", "2")  // 只处理押注中状态的对赌
                    .eq("sfsc", "0"));

                if (list.isEmpty()) {
                    log.info("没有需要停止押注的对赌");
                    return Result.OK("没有需要停止押注的对赌");
                }

                int successCount = 0;
                int skipCount = 0;
                List<String> errorMessages = new ArrayList<>();
                for (WlgbDdfq wlgbDdfq : list) {
                    try {
                        //押注截止时间
                        Date yzjzsj = wlgbDdfq.getYzjzsj();
                        //获取当前时间
                        Date date = new Date();
                        if (yzjzsj != null) {
                            List<WlgbDdyz> listyz = wlgbDdyzService.list(new QueryWrapper<WlgbDdyz>().eq("lsh", wlgbDdfq.getLsh()));
                            if (listyz.isEmpty()) {
                                continue;
                            } else {
                                List<WlgbDdyz> list1 = wlgbDdyzService.list(new QueryWrapper<WlgbDdyz>().eq("lsh", wlgbDdfq.getLsh()).groupBy("dxid"));
                                if (list1.size() < 2) {
                                    continue;
                                }
                            }
                            //判断押注时间是否符合条件
                            if (date.compareTo(yzjzsj) > 0) {
                                // 🔥 关键：使用CAS确保幂等性，只有状态为2的才能更新为3
                                boolean updated = wlgbDdfqService.update(
                                    new WlgbDdfq().setYzzt(3), // 设置为停止押注状态
                                    new QueryWrapper<WlgbDdfq>()
                                        .eq("lsh", wlgbDdfq.getLsh())
                                        .eq("yzzt", 2)  // 只有状态为2的才能更新
                                );

                                if (!updated) {
                                    // 如果更新失败，说明已经被处理了
                                    skipCount++;
                                    log.info("对赌流水号{}已停止押注，跳过", wlgbDdfq.getLsh());
                                    continue;
                                }
                                List<Map<String, Object>> list3 = wlgbDdfqService.queryWlgbDdfqTzyz(wlgbDdfq.getLsh());
                                String url3 = "";
                                try {
                                    url3 = yzbg(list3, wlgbDdfq);
                                } catch (Exception e) {
                                    log.warn("生成押注表格失败，对赌流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage());
                                }
                                WlgbDdyzbgjl wlgbDdyzbgjl = new WlgbDdyzbgjl();
                                //判断是否存在押注表格
                                Integer count = wlgbDdyzbgjlService.count(new QueryWrapper<WlgbDdyzbgjl>().eq("lsh", wlgbDdfq.getLsh()));
                                if (count < 1) {
                                    //保存押注表格记录
                                    wlgbDdyzbgjl.setId(UUID.randomUUID().toString().replace("-", ""));
                                    wlgbDdyzbgjl.setLsh(wlgbDdfq.getLsh());
                                    wlgbDdyzbgjl.setUrl(url3);
                                    wlgbDdyzbgjlService.save(wlgbDdyzbgjl);
                                } else {
                                    wlgbDdyzbgjl.setUrl(url3);
                                    wlgbDdyzbgjlService.update(wlgbDdyzbgjl, new QueryWrapper<WlgbDdyzbgjl>().eq("lsh", wlgbDdfq.getLsh()));
                                }
                                List list1 = new ArrayList();
                                String url = "[三多][三多] " + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "的押注截止时间已到，" +
                                        "停止押注！\n下图为他们的对赌押注详细信息，各位也可前往【工作台】→【企业文化-对赌中心】→【对赌进行中】查看该对赌的详细押注信息\n\n" +
                                        "![screenshot](" + url3 + ")\n";
                                String title = wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "的对赌已经停止押注了，请查看！";
                                if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                                    url = "[三多][三多] " + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "的押注截止时间已到，停止押注！\n" +
                                            "下图为他们的对赌押注详细信息，各位也可前往【工作台】→【企业文化-对赌中心】→【对赌进行中】查看该对赌的详细押注信息\n\n" +
                                            "![screenshot](" + url3 + ")\n";
                                    title = wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "的对赌已经停止押注了，请查看！";
                                }

                                // 发送钉钉消息（即使失败也不影响状态）
                                try {
                                    DingDingUtil.sendMark(jmwebhook, jmsecret, title, url, list1, true);
                                    DingDingUtil.sendMark(webhook, secret, title, url, list1, true);
                                } catch (Exception e) {
                                    log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage());
                                }

                                successCount++;
                                log.info("对赌流水号{}停止押注成功", wlgbDdfq.getLsh());
                            }
                        }
                    } catch (Exception e) {
                        log.error("停止押注失败，流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage(), e);
                        errorMessages.add("流水号" + wlgbDdfq.getLsh() + "停止押注失败：" + e.getMessage());

                        // 发生异常时回滚状态
                        try {
                            wlgbDdfqService.update(
                                new WlgbDdfq().setYzzt(2), // 回滚到押注中状态
                                new QueryWrapper<WlgbDdfq>().eq("lsh", wlgbDdfq.getLsh())
                            );
                            log.info("对赌流水号{}状态已回滚", wlgbDdfq.getLsh());
                        } catch (Exception rollbackEx) {
                            log.error("状态回滚失败，流水号：{}", wlgbDdfq.getLsh(), rollbackEx);
                        }
                    }
                }

                long duration = System.currentTimeMillis() - startTime;
                String message = String.format("停止押注完成！成功：%d，跳过：%d，失败：%d，耗时：%dms",
                        successCount, skipCount, errorMessages.size(), duration);

                if (!errorMessages.isEmpty()) {
                    message += "，失败详情：" + String.join("; ", errorMessages);
                }

                log.info("停止押注执行完成：{}", message);
                return Result.OK(message);
            }

            return Result.OK("非生产环境，跳过执行");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("停止押注执行异常，耗时：{}ms", duration, e);
            return Result.error("停止押注失败：" + e.getMessage());
        }
    }

    //押注截止前30分钟提醒 - 幂等性优化版本
    @RequestMapping("yztx")
    public Result Yztx() {
        log.info("开始执行押注截止前提醒，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            //线下测试群聊
            String webhook = "";
            String secret = "";
            String jmwebhook = "";
            String jmsecret = "";
            //加盟群聊
            jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
            jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";

            if ("prod".equals(hjConfig.getActive())) {
                webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
                secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";
                String lj = "https://jztdpp.aliwork.com/APP_XD2KFYUV4YZ1FWN46DEI/custom/FORM-TL866181NMASVIWDZFOADE2IJNTY1MORNUSRKAG";
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = new Date();
                String d = sdf.format(date);

                // 🔥 关键优化：只获取状态为2的对赌，确保幂等性
                List<WlgbDdfq> list = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                    .eq("sfsc", 0)
                    .eq("zt", "2")
                    .eq("yzzt", "2")  // 只处理押注中状态的对赌
                    .like("yzjzsj", d));

                if (list.isEmpty()) {
                    log.info("没有需要提醒的对赌");
                    return Result.OK("没有需要提醒的对赌");
                }

                int successCount = 0;
                List<String> errorMessages = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    try {
                        WlgbDdfq wlgbDdfq = list.get(i);
                        List list1 = new ArrayList();
                        String title = wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid();
                        String url = "#### 家银们，以下为" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "的赔率：\n"
                                + "## 挑战方：" + wlgbDdfq.getTzfid() + " 赔率：1:" + wlgbDdfq.getTzfpl() + "\n"
                                + "## 应战方：" + wlgbDdfq.getBtzfid() + " 赔率：1:" + wlgbDdfq.getYzfpl() + "\n";
                        if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                            title = wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2();
                            url = "#### 家银们，以下为" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "的赔率：\n"
                                    + "## 挑战方：" + wlgbDdfq.getTzfid() + " 赔率：1:" + wlgbDdfq.getTzfpl() + "\n"
                                    + "## 应战方：" + wlgbDdfq.getBtzfid() + " 赔率：1:" + wlgbDdfq.getYzfpl() + "\n"
                                    + "## 应战方2：" + wlgbDdfq.getBtzfid2() + " 赔率：1:" + wlgbDdfq.getYzfpl2() + "\n";
                        }
                        url += "###  押注进入最后2小时倒计时啦，试试手气赚点小钱钱（零食、鸡腿、雪糕随你挑）[来呀][来呀][来呀][Get][自信]" +
                                "![screenshot](http://jiuyun2.qianquan888.com/upload/test/bjt_1649926756030.jpg)";

                        // 发送钉钉消息（即使失败也不影响整体流程）
                        try {
                            DingDingUtil.sendActionCard(jmwebhook, jmsecret, title, url, "点击我去押注", lj, "0");
                            DingDingUtil.sendMsg(jmwebhook, jmsecret, "快来押注吧！！！", list1, true);
                            DingDingUtil.sendActionCard(webhook, secret, title, url, "点击我去押注", lj, "0");
                            DingDingUtil.sendMsg(webhook, secret, "快来押注吧！！！", list1, true);
                            successCount++;
                            log.info("对赌流水号{}押注提醒发送成功", wlgbDdfq.getLsh());
                        } catch (Exception e) {
                            log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage());
                            errorMessages.add("流水号" + wlgbDdfq.getLsh() + "提醒发送失败：" + e.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("押注提醒处理失败，流水号：{}, 错误：{}", list.get(i).getLsh(), e.getMessage(), e);
                        errorMessages.add("流水号" + list.get(i).getLsh() + "处理失败：" + e.getMessage());
                    }
                }

                long duration = System.currentTimeMillis() - startTime;
                String message = String.format("押注提醒完成！成功：%d，失败：%d，耗时：%dms",
                        successCount, errorMessages.size(), duration);

                if (!errorMessages.isEmpty()) {
                    message += "，失败详情：" + String.join("; ", errorMessages);
                }

                log.info("押注提醒执行完成：{}", message);
                return Result.OK(message);
            }

            return Result.OK("非生产环境，跳过执行");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("押注提醒执行异常，耗时：{}ms", duration, e);
            return Result.error("押注提醒失败：" + e.getMessage());
        }
    }

    //自动结算 - 紧急修复版本（添加幂等性控制）
    @RequestMapping("autoJs")
    public Result AutoJs() {
        log.info("开始执行自动结算，时间：{}", new java.util.Date());
        String webhook = "";
        String secret = "";
        String jmwebhook = "";
        String jmsecret = "";
        //加盟群配置
        jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
        jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";
        //直营群配置
        webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
        secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";

        // 🔥 关键优化：使用数据库事务确保幂等性
        // 先查询待结算的对赌，然后在事务中原子性地更新状态
        List<WlgbDdfq> listfq = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                .eq("zt", 2)
                .eq("yzzt", 8)  // 待结算状态
                .eq("sfsc", 0));

        if (listfq.isEmpty()) {
            return Result.error("没有需要结算的对赌");
        }

        int successCount = 0;
        int skipCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (WlgbDdfq wlgbDdfq : listfq) {
            try {
                // 🔥 关键：使用CAS（Compare And Swap）确保幂等性
                // 只有状态为8（待结算）的记录才能被更新为5（已结算）
                boolean updated = wlgbDdfqService.update(
                    new WlgbDdfq().setYzzt(5), // 设置为已结算状态
                    new QueryWrapper<WlgbDdfq>()
                        .eq("lsh", wlgbDdfq.getLsh())
                        .eq("yzzt", 8)  // 只有状态为8的才能更新
                );

                if (!updated) {
                    // 如果更新失败，说明已经被其他线程/进程处理了
                    skipCount++;
                    continue;
                }

                // 只有成功更新状态的记录才进行后续处理
                List list1 = new ArrayList();
                String title = wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + "的对赌已结束";
                String content = "[广播][广播] " + wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + " 的个人对赌已经结束啦，胜利方为" + wlgbDdfq.getSlf() +
                        "，大家可前往【企业文化】→【对赌中心】→【对赌已完结】中查看此对赌的详细对赌数据， " +
                        "线上对赌的结算金额都将通过系统直接进行发放，" +
                        "想要查询威廉币是否到账请前往【对赌中心首页-威廉币余额】中进行查看哟~[抱拳][抱拳]";

                if ("团队对赌".equals(wlgbDdfq.getDdlx())) {
                    content = "[广播][广播] (" + wlgbDdfq.getTzfcy() + ") VS (" + wlgbDdfq.getBtzfcy() + ") 的团队对赌已经结束啦，胜利方为" + wlgbDdfq.getSlf() +
                            "方，大家可前往【企业文化】→【对赌中心】→【对赌已完结】中查看此对赌的详细对赌数据， " +
                            "线上对赌的结算金额都将通过系统直接进行发放，" +
                            "想要查询威廉币是否到账请前往【对赌中心首页-威廉币余额】中进行查看哟~[抱拳][抱拳]";
                }
                if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                    title = wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "的对赌已结束";
                    content = "[广播][广播] (" + wlgbDdfq.getTzfcy() + ") VS (" + wlgbDdfq.getBtzfcy() + " ) VS ( " + wlgbDdfq.getBtzfcy2() + " ) 的多人对赌已经结束啦，胜利方为" + wlgbDdfq.getSlf() +
                            "方，大家可前往【企业文化】→【对赌中心】→【对赌已完结】中查看此对赌的详细对赌数据， " +
                            "线上对赌的结算金额都将通过系统直接进行发放，" +
                            "想要查询威廉币是否到账请前往【对赌中心首页-威廉币余额】中进行查看哟~[抱拳][抱拳]";
                }
                if (wlgbDdfq.getJsbg() != null && !"".equals(wlgbDdfq.getJsbg())) {
                    content += "\n\n以下为结算明细，恭喜赢得胜利的伙伴们\n\n" +
                            "![screenshot](" + wlgbDdfq.getJsbg() + ")";
                }

                // 发送钉钉通知（即使失败也不影响结算）
                try {
                    DingDingUtil.sendMark(jmwebhook, jmsecret, title, content, list1, true);
                    DingDingUtil.sendMark(webhook, secret, title, content, list1, true);
                } catch (Exception e) {
                    // 钉钉发送失败不影响结算流程
                    log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage());
                }

                //结算威廉币 结算等级 发送工作通知
                ddjs(wlgbDdfq);
                successCount++;

            } catch (Exception e) {
                log.error("对赌结算失败，流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage(), e);
                errorMessages.add("流水号" + wlgbDdfq.getLsh() + "结算失败：" + e.getMessage());

                // 发生异常时回滚状态
                try {
                    wlgbDdfqService.update(
                        new WlgbDdfq().setYzzt(8), // 回滚到待结算状态
                        new QueryWrapper<WlgbDdfq>().eq("lsh", wlgbDdfq.getLsh())
                    );
                } catch (Exception rollbackEx) {
                    log.error("状态回滚失败，流水号：{}", wlgbDdfq.getLsh(), rollbackEx);
                }
            }
        }

        // 返回处理结果
        String message = String.format("结算完成！成功：%d，跳过：%d，失败：%d",
                successCount, skipCount, errorMessages.size());
        if (!errorMessages.isEmpty()) {
            message += "，失败详情：" + String.join("; ", errorMessages);
        }

        return Result.OK(message);
    }

    /**
     * 纯粹的公司账户重新结算接口
     * 只重新计算和更新 wlgb_ddcompanycount 表
     * 不修改其他表，不发送任何消息
     */
    @RequestMapping("recalculateCompanyAccount")
    public Result RecalculateCompanyAccount() {
        log.info("开始执行公司账户重新结算，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 备份当前数据
            String backupTableName = createCompanyAccountBackup();
            log.info("已创建备份表：{}", backupTableName);

            // 2. 清空当前公司账户表
            wlgbDdcompanycountService.remove(new QueryWrapper<>());
            log.info("已清空 wlgb_ddcompanycount 表");

            // 3. 重新计算并插入所有结算记录
            int totalRecords = recalculateAllCompanyRecords();

            long endTime = System.currentTimeMillis();
            String message = String.format("公司账户重新结算完成！共重新生成 %d 条记录，耗时：%d ms",
                    totalRecords, (endTime - startTime));

            log.info(message);
            return Result.OK(message);

        } catch (Exception e) {
            log.error("公司账户重新结算失败", e);
            return Result.error("重新结算失败：" + e.getMessage());
        }
    }

    /**
     * 简化版公司账户重新结算接口
     * 只清理重复数据并重新生成必要的记录
     * 更安全的选择，不会完全清空表
     *
     * @param lsh 对赌流水号，如果为空则处理所有数据
     */
    @RequestMapping("recalculateCompanyAccountSafe")
    public Result RecalculateCompanyAccountSafe(String lsh) {
        log.info("开始执行安全模式公司账户重新结算，对赌流水号：{}，时间：{}", lsh, new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 分析当前状态
            Map<String, Object> beforeStatus = analyzeCompanyAccountStatus(lsh);
            log.info("重新结算前状态：{}", beforeStatus);

            // 2. 清理重复数据
            int duplicatesRemoved = removeDuplicateCompanyRecords(lsh);
            log.info("清理重复数据：{} 条", duplicatesRemoved);

            // 3. 补充缺失的记录
            int missingRecordsAdded = addMissingCompanyRecords(lsh);
            log.info("补充缺失记录：{} 条", missingRecordsAdded);

            // 4. 分析结果状态
            Map<String, Object> afterStatus = analyzeCompanyAccountStatus(lsh);
            log.info("重新结算后状态：{}", afterStatus);

            long endTime = System.currentTimeMillis();
            String message = String.format("安全模式公司账户重新结算完成！对赌流水号：%s，清理重复：%d 条，补充缺失：%d 条，耗时：%d ms",
                    lsh != null ? lsh : "全部", duplicatesRemoved, missingRecordsAdded, (endTime - startTime));

            Map<String, Object> result = new HashMap<>();
            result.put("message", message);
            result.put("lsh", lsh);
            result.put("beforeStatus", beforeStatus);
            result.put("afterStatus", afterStatus);
            result.put("duplicatesRemoved", duplicatesRemoved);
            result.put("missingRecordsAdded", missingRecordsAdded);

            log.info(message);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("安全模式公司账户重新结算失败，对赌流水号：{}", lsh, e);
            return Result.error("重新结算失败：" + e.getMessage());
        }
    }

    /**
     * 查看公司账户当前状态
     * 只读接口，不做任何修改
     */
    @RequestMapping("checkCompanyAccountStatus")
    public Result CheckCompanyAccountStatus() {
        try {
            Map<String, Object> status = analyzeCompanyAccountStatus();

            // 检查重复数据
            List<Map<String, Object>> duplicates = checkDuplicateCompanyRecords();
            status.put("duplicateRecords", duplicates);
            status.put("hasDuplicates", !duplicates.isEmpty());

            // 检查数据完整性
            Map<String, Object> integrityCheck = checkDataIntegrity();
            status.put("integrityCheck", integrityCheck);

            return Result.OK(status);

        } catch (Exception e) {
            log.error("检查公司账户状态失败", e);
            return Result.error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查数据完整性
     */
    private Map<String, Object> checkDataIntegrity() {
        Map<String, Object> result = new HashMap<>();

        // 检查离职人员结算记录完整性
        List<WlgbJsfqjl> resignedSettlements = wlgbJsfqjlService.list(
            new QueryWrapper<WlgbJsfqjl>().eq("zt", 1)
        );

        int missingResignedRecords = 0;
        for (WlgbJsfqjl settlement : resignedSettlements) {
            WlgbDdryxx user = wlgbDdryxxService.getOne(
                new QueryWrapper<WlgbDdryxx>().eq("userid", settlement.getSlyqrid())
            );

            if (user != null && user.getSfsc() == 1) {
                long existingCount = wlgbDdcompanycountService.count(
                    new QueryWrapper<WlgbDdcompanycount>()
                        .eq("lsh", settlement.getLsh())
                        .eq("userid", settlement.getSlyqrid())
                        .eq("bz", "离职人员结算")
                );

                if (existingCount == 0) {
                    missingResignedRecords++;
                }
            }
        }

        // 检查对赌结算记录完整性
        List<WlgbDdfq> settledGambles = wlgbDdfqService.list(
            new QueryWrapper<WlgbDdfq>()
                .eq("yzzt", 5)
                .eq("sfsc", 0)
        );

        int missingGamblingRecords = 0;
        for (WlgbDdfq gamble : settledGambles) {
            long existingCount = wlgbDdcompanycountService.count(
                new QueryWrapper<WlgbDdcompanycount>()
                    .eq("lsh", gamble.getLsh())
                    .eq("bz", "对赌结算")
            );

            if (existingCount == 0) {
                missingGamblingRecords++;
            }
        }

        result.put("totalResignedSettlements", resignedSettlements.size());
        result.put("missingResignedRecords", missingResignedRecords);
        result.put("totalSettledGambles", settledGambles.size());
        result.put("missingGamblingRecords", missingGamblingRecords);
        result.put("isComplete", missingResignedRecords == 0 && missingGamblingRecords == 0);

        return result;
    }

    /**
     * 优化后的自动结算接口 - 确保幂等性
     * 解决云函数重试导致的重复结算问题
     * 完全替代原autoJs接口，包含所有功能
     */
    @RequestMapping("autoJsOptimized")
    public Result AutoJsOptimized() {
        log.info("开始执行优化后的自动结算，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            String webhook = "";
            String secret = "";
            String jmwebhook = "";
            String jmsecret = "";
            //加盟群配置
            jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
            jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";
            //直营群配置
            webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
            secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";

            // 🔥 关键优化：使用数据库事务确保幂等性
            List<WlgbDdfq> listfq = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                    .eq("zt", 2)
                    .eq("yzzt", 8)  // 待结算状态
                    .eq("sfsc", 0));

            if (listfq.isEmpty()) {
                log.info("没有需要结算的对赌");
                return Result.error("没有需要结算的对赌");
            }

            int successCount = 0;
            int skipCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (WlgbDdfq wlgbDdfq : listfq) {
                try {
                    // 🔥 关键：使用CAS（Compare And Swap）确保幂等性
                    boolean updated = wlgbDdfqService.update(
                        new WlgbDdfq().setYzzt(5), // 设置为已结算状态
                        new QueryWrapper<WlgbDdfq>()
                            .eq("lsh", wlgbDdfq.getLsh())
                            .eq("yzzt", 8)  // 只有状态为8的才能更新
                    );

                    if (!updated) {
                        // 如果更新失败，说明已经被处理了
                        skipCount++;
                        log.info("对赌流水号{}已被处理，跳过", wlgbDdfq.getLsh());
                        continue;
                    }

                    // 只有成功更新状态的记录才进行后续处理
                    List<String> list1 = new ArrayList<>();
                    String title = wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + "的对赌已结束";
                    String content = "[广播][广播] " + wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + " 的个人对赌已经结束啦，胜利方为" + wlgbDdfq.getSlf() +
                            "，大家可前往【企业文化】→【对赌中心】→【对赌已完结】中查看此对赌的详细对赌数据， " +
                            "线上对赌的结算金额都将通过系统直接进行发放，" +
                            "想要查询威廉币是否到账请前往【对赌中心首页-威廉币余额】中进行查看哟~[抱拳][抱拳]";

                    if ("团队对赌".equals(wlgbDdfq.getDdlx())) {
                        content = "[广播][广播] (" + wlgbDdfq.getTzfcy() + ") VS (" + wlgbDdfq.getBtzfcy() + ") 的团队对赌已经结束啦，胜利方为" + wlgbDdfq.getSlf() +
                                "方，大家可前往【企业文化】→【对赌中心】→【对赌已完结】中查看此对赌的详细对赌数据， " +
                                "线上对赌的结算金额都将通过系统直接进行发放，" +
                                "想要查询威廉币是否到账请前往【对赌中心首页-威廉币余额】中进行查看哟~[抱拳][抱拳]";
                    }
                    if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                        title = wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "的对赌已结束";
                        content = "[广播][广播] (" + wlgbDdfq.getTzfcy() + ") VS (" + wlgbDdfq.getBtzfcy() + " ) VS ( " + wlgbDdfq.getBtzfcy2() + " ) 的多人对赌已经结束啦，胜利方为" + wlgbDdfq.getSlf() +
                                "方，大家可前往【企业文化】→【对赌中心】→【对赌已完结】中查看此对赌的详细对赌数据， " +
                                "线上对赌的结算金额都将通过系统直接进行发放，" +
                                "想要查询威廉币是否到账请前往【对赌中心首页-威廉币余额】中进行查看哟~[抱拳][抱拳]";
                    }
                    if (wlgbDdfq.getJsbg() != null && !"".equals(wlgbDdfq.getJsbg())) {
                        content += "\n\n以下为结算明细，恭喜赢得胜利的伙伴们\n\n" +
                                "![screenshot](" + wlgbDdfq.getJsbg() + ")";
                    }

                    // 发送钉钉通知（即使失败也不影响结算）
                    try {
                        DingDingUtil.sendMark(jmwebhook, jmsecret, title, content, list1, true);
                        DingDingUtil.sendMark(webhook, secret, title, content, list1, true);
                    } catch (Exception e) {
                        log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage());
                    }

                    // 结算威廉币 结算等级 发送工作通知
                    ddjs(wlgbDdfq);
                    successCount++;
                    log.info("对赌流水号{}结算成功", wlgbDdfq.getLsh());

                } catch (Exception e) {
                    log.error("对赌结算失败，流水号：{}, 错误：{}", wlgbDdfq.getLsh(), e.getMessage(), e);
                    errorMessages.add("流水号" + wlgbDdfq.getLsh() + "结算失败：" + e.getMessage());

                    // 发生异常时回滚状态
                    try {
                        wlgbDdfqService.update(
                            new WlgbDdfq().setYzzt(8), // 回滚到待结算状态
                            new QueryWrapper<WlgbDdfq>().eq("lsh", wlgbDdfq.getLsh())
                        );
                        log.info("对赌流水号{}状态已回滚", wlgbDdfq.getLsh());
                    } catch (Exception rollbackEx) {
                        log.error("状态回滚失败，流水号：{}", wlgbDdfq.getLsh(), rollbackEx);
                    }
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("结算完成！成功：%d，跳过：%d，失败：%d，耗时：%dms",
                    successCount, skipCount, errorMessages.size(), duration);

            if (!errorMessages.isEmpty()) {
                message += "，失败详情：" + String.join("; ", errorMessages);
            }

            log.info("自动结算执行完成：{}", message);

            // 如果执行时间超过4分钟，发送告警
            if (duration > 240000) {
                log.warn("autoJsOptimized执行时间过长：{}ms，可能存在性能问题", duration);
            }

            return Result.OK(message);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("自动结算执行异常，耗时：{}ms", duration, e);
            return Result.error("自动结算失败：" + e.getMessage());
        }
    }

    /**
     * 公司账户数据修复接口
     * 修复因超时重试导致的重复数据
     */
    @RequestMapping("fixCompanyAccount")
    public Result FixCompanyAccount() {
        log.info("开始修复公司账户数据");

        try {
            // 1. 先备份数据
            String backupTableName = "wlgb_ddcompanycount_backup_" +
                new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new java.util.Date());

            // 2. 检查重复数据
            List<Map<String, Object>> duplicates = checkDuplicateCompanyRecords();

            // 3. 分析当前状态
            Map<String, Object> currentStatus = analyzeCompanyAccountStatus();

            // 4. 生成修复报告
            Map<String, Object> result = new HashMap<>();
            result.put("backupTable", backupTableName);
            result.put("duplicateRecords", duplicates);
            result.put("currentStatus", currentStatus);
            result.put("needFix", !duplicates.isEmpty());

            if (duplicates.isEmpty()) {
                result.put("message", "未发现重复数据，无需修复");
            } else {
                result.put("message", String.format("发现%d组重复数据，建议执行修复", duplicates.size()));
                result.put("fixSuggestion", "请先确认数据后，调用 /fixCompanyAccountExecute 执行修复");
            }

            log.info("公司账户数据检查完成：{}", result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("公司账户数据修复检查失败", e);
            return Result.error("数据检查失败：" + e.getMessage());
        }
    }

    /**
     * 执行公司账户数据修复
     */
    @RequestMapping("fixCompanyAccountExecute")
    public Result FixCompanyAccountExecute() {
        log.info("开始执行公司账户数据修复");

        try {
            // 1. 再次检查重复数据
            List<Map<String, Object>> duplicates = checkDuplicateCompanyRecords();
            if (duplicates.isEmpty()) {
                return Result.OK("未发现重复数据，无需修复");
            }

            // 2. 执行修复
            int fixedCount = 0;
            for (Map<String, Object> duplicate : duplicates) {
                String lsh = (String) duplicate.get("lsh");
                String bz = (String) duplicate.get("bz");
                Integer lx = (Integer) duplicate.get("lx");
                String userid = (String) duplicate.get("userid");

                // 删除重复记录，保留最早的
                int deleted = removeDuplicateCompanyRecords(lsh, bz, lx, userid);
                fixedCount += deleted;
            }

            // 3. 验证修复结果
            List<Map<String, Object>> remainingDuplicates = checkDuplicateCompanyRecords();
            Map<String, Object> newStatus = analyzeCompanyAccountStatus();

            Map<String, Object> result = new HashMap<>();
            result.put("fixedRecords", fixedCount);
            result.put("remainingDuplicates", remainingDuplicates.size());
            result.put("newStatus", newStatus);
            result.put("success", remainingDuplicates.isEmpty());

            String message = String.format("修复完成！删除了%d条重复记录，剩余%d组重复数据",
                    fixedCount, remainingDuplicates.size());
            result.put("message", message);

            log.info("公司账户数据修复完成：{}", result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("公司账户数据修复执行失败", e);
            return Result.error("数据修复失败：" + e.getMessage());
        }
    }

    /**
     * 检查重复的公司账户记录
     */
    private List<Map<String, Object>> checkDuplicateCompanyRecords() {
        // 这里需要使用原生SQL查询重复数据
        // 由于MyBatis-Plus的限制，建议在Mapper中添加自定义方法
        return new ArrayList<>(); // 临时返回空列表
    }

    /**
     * 创建公司账户备份表
     */
    private String createCompanyAccountBackup() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        String backupTableName = "wlgb_ddcompanycount_backup_" + timestamp;

        // 这里应该执行 SQL 创建备份表，但为了简化，我们记录日志
        log.info("创建备份表：{}", backupTableName);
        return backupTableName;
    }

    /**
     * 重新计算所有公司账户记录
     */
    private int recalculateAllCompanyRecords() {
        int totalRecords = 0;

        // 1. 重新生成离职人员结算记录
        totalRecords += recalculateResignedEmployeeRecords();

        // 2. 重新生成对赌结算记录
        totalRecords += recalculateGamblingSettlementRecords();

        // 3. 重新生成威廉币充值记录（如果有的话）
        totalRecords += recalculateWilliamCoinRechargeRecords();

        return totalRecords;
    }

    /**
     * 重新计算离职人员结算记录
     */
    private int recalculateResignedEmployeeRecords() {
        int count = 0;

        // 查询所有离职人员的结算记录
        List<WlgbJsfqjl> resignedSettlements = wlgbJsfqjlService.list(
            new QueryWrapper<WlgbJsfqjl>()
                .eq("zt", 1)  // 胜利方
                .isNotNull("lsh")
        );

        for (WlgbJsfqjl settlement : resignedSettlements) {
            // 检查该用户是否为离职人员
            WlgbDdryxx user = wlgbDdryxxService.getOne(
                new QueryWrapper<WlgbDdryxx>().eq("userid", settlement.getSlyqrid())
            );

            if (user != null && user.getSfsc() == 1) {  // 离职人员
                WlgbDdcompanycount companyRecord = new WlgbDdcompanycount();
                companyRecord.setId(IdConfig.uuId());
                companyRecord.setJsje(settlement.getJe());
                companyRecord.setZcje(0.0);
                companyRecord.setZje(0.0);
                companyRecord.setBz("离职人员结算");
                companyRecord.setLsh(settlement.getLsh());
                companyRecord.setUserid(settlement.getSlyqrid());
                companyRecord.setLx(1);  // 收入
                companyRecord.setCreateTime(new Date());

                wlgbDdcompanycountService.save(companyRecord);
                count++;
            }
        }

        log.info("重新生成离职人员结算记录：{} 条", count);
        return count;
    }

    /**
     * 重新计算对赌结算记录
     */
    private int recalculateGamblingSettlementRecords() {
        int count = 0;

        // 查询所有已结算的对赌
        List<WlgbDdfq> settledGambles = wlgbDdfqService.list(
            new QueryWrapper<WlgbDdfq>()
                .eq("yzzt", 5)  // 已结算状态
                .eq("sfsc", 0)
        );

        for (WlgbDdfq gamble : settledGambles) {
            // 重新计算该对赌的公司账户记录
            WlgbDdcompanycount companyRecord = calculateGamblingCompanyRecord(gamble);
            if (companyRecord != null) {
                wlgbDdcompanycountService.save(companyRecord);
                count++;
            }
        }

        log.info("重新生成对赌结算记录：{} 条", count);
        return count;
    }

    /**
     * 计算单个对赌的公司账户记录
     */
    private WlgbDdcompanycount calculateGamblingCompanyRecord(WlgbDdfq gamble) {
        try {
            WlgbDdcompanycount queryResult = wlgbDdfqService.queryJsCompany(gamble.getLsh());
            if (queryResult == null) {
                return null;
            }

            Double zcje = queryResult.getZcje();  // 支出金额
            Double zje = queryResult.getZje();    // 总金额

            if (zcje == null || zje == null) {
                return null;
            }

            // 计算公司剩余金额
            Double remainingAmount = zje - zcje;

            // 格式化金额（保留3位小数）
            String formattedAmount = String.format("%.3f", remainingAmount);
            Double finalAmount = Double.valueOf(formattedAmount);

            WlgbDdcompanycount companyRecord = new WlgbDdcompanycount();
            companyRecord.setId(IdConfig.uuId());
            companyRecord.setJsje(finalAmount);
            companyRecord.setZcje(0.0);
            companyRecord.setZje(0.0);
            companyRecord.setBz("对赌结算");
            companyRecord.setLsh(gamble.getLsh());
            companyRecord.setLx(1);  // 收入
            companyRecord.setCreateTime(new Date());

            return companyRecord;

        } catch (Exception e) {
            log.error("计算对赌 {} 的公司账户记录失败", gamble.getLsh(), e);
            return null;
        }
    }

    /**
     * 重新计算威廉币充值记录
     */
    private int recalculateWilliamCoinRechargeRecords() {
        // 这里可以根据实际业务需求添加威廉币充值相关的公司账户记录重新计算逻辑
        // 目前暂时返回0
        log.info("重新生成威廉币充值记录：0 条");
        return 0;
    }

    /**
     * 清理重复的公司账户记录
     */
    private int removeDuplicateCompanyRecords() {
        return removeDuplicateCompanyRecords(null);
    }

    /**
     * 清理重复的公司账户记录（支持按流水号过滤）
     * @param lsh 对赌流水号，如果为空则处理所有数据
     */
    private int removeDuplicateCompanyRecords(String lsh) {
        int removedCount = 0;

        // 构建查询条件
        QueryWrapper<WlgbDdcompanycount> queryWrapper = new QueryWrapper<>();
        if (lsh != null && !lsh.trim().isEmpty()) {
            queryWrapper.eq("lsh", lsh);
        }

        // 查找重复记录（相同的lsh、userid、bz、lx、jsje但不同ID）
        List<WlgbDdcompanycount> allRecords = wlgbDdcompanycountService.list(queryWrapper);
        Map<String, List<WlgbDdcompanycount>> groupedRecords = new HashMap<>();

        // 按关键字段分组
        for (WlgbDdcompanycount record : allRecords) {
            String key = String.format("%s_%s_%s_%d_%.3f",
                record.getLsh() != null ? record.getLsh() : "null",
                record.getUserid() != null ? record.getUserid() : "null",
                record.getBz() != null ? record.getBz() : "null",
                record.getLx() != null ? record.getLx() : 0,
                record.getJsje() != null ? record.getJsje() : 0.0);

            groupedRecords.computeIfAbsent(key, k -> new ArrayList<>()).add(record);
        }

        // 删除重复记录，保留最早的
        for (List<WlgbDdcompanycount> duplicates : groupedRecords.values()) {
            if (duplicates.size() > 1) {
                // 按创建时间排序，保留最早的
                duplicates.sort((a, b) -> {
                    Date timeA = a.getCreateTime() != null ? a.getCreateTime() : new Date(0);
                    Date timeB = b.getCreateTime() != null ? b.getCreateTime() : new Date(0);
                    return timeA.compareTo(timeB);
                });

                // 删除除第一个之外的所有记录
                for (int i = 1; i < duplicates.size(); i++) {
                    wlgbDdcompanycountService.removeById(duplicates.get(i).getId());
                    removedCount++;
                    log.info("删除重复记录：流水号={}, 用户={}, 备注={}, ID={}",
                        duplicates.get(i).getLsh(),
                        duplicates.get(i).getUserid(),
                        duplicates.get(i).getBz(),
                        duplicates.get(i).getId());
                }
            }
        }

        return removedCount;
    }

    /**
     * 补充缺失的公司账户记录
     */
    private int addMissingCompanyRecords() {
        return addMissingCompanyRecords(null);
    }

    /**
     * 补充缺失的公司账户记录（支持按流水号过滤）
     * @param lsh 对赌流水号，如果为空则处理所有数据
     */
    private int addMissingCompanyRecords(String lsh) {
        int addedCount = 0;

        // 1. 检查离职人员结算记录是否完整
        addedCount += addMissingResignedEmployeeRecords(lsh);

        // 2. 检查对赌结算记录是否完整
        addedCount += addMissingGamblingRecords(lsh);

        return addedCount;
    }

    /**
     * 补充缺失的离职人员结算记录
     */
    private int addMissingResignedEmployeeRecords() {
        return addMissingResignedEmployeeRecords(null);
    }

    /**
     * 补充缺失的离职人员结算记录（支持按流水号过滤）
     * @param lsh 对赌流水号，如果为空则处理所有数据
     */
    private int addMissingResignedEmployeeRecords(String lsh) {
        int addedCount = 0;

        // 构建查询条件
        QueryWrapper<WlgbJsfqjl> queryWrapper = new QueryWrapper<WlgbJsfqjl>().eq("zt", 1);
        if (lsh != null && !lsh.trim().isEmpty()) {
            queryWrapper.eq("lsh", lsh);
        }

        // 查询所有离职人员的胜利结算记录
        List<WlgbJsfqjl> resignedSettlements = wlgbJsfqjlService.list(queryWrapper);

        for (WlgbJsfqjl settlement : resignedSettlements) {
            // 检查用户是否为离职人员
            WlgbDdryxx user = wlgbDdryxxService.getOne(
                new QueryWrapper<WlgbDdryxx>().eq("userid", settlement.getSlyqrid())
            );

            if (user != null && user.getSfsc() == 1) {
                // 检查是否已存在对应的公司账户记录
                long existingCount = wlgbDdcompanycountService.count(
                    new QueryWrapper<WlgbDdcompanycount>()
                        .eq("lsh", settlement.getLsh())
                        .eq("userid", settlement.getSlyqrid())
                        .eq("bz", "离职人员结算")
                );

                if (existingCount == 0) {
                    // 创建缺失的记录
                    WlgbDdcompanycount companyRecord = new WlgbDdcompanycount();
                    companyRecord.setId(IdConfig.uuId());
                    companyRecord.setJsje(settlement.getJe());
                    companyRecord.setZcje(0.0);
                    companyRecord.setZje(0.0);
                    companyRecord.setBz("离职人员结算");
                    companyRecord.setLsh(settlement.getLsh());
                    companyRecord.setUserid(settlement.getSlyqrid());
                    companyRecord.setLx(1);
                    companyRecord.setCreateTime(new Date());

                    wlgbDdcompanycountService.save(companyRecord);
                    addedCount++;
                    log.info("补充离职人员结算记录：流水号={}, 用户={}, 金额={}",
                        settlement.getLsh(), settlement.getSlyqrid(), settlement.getJe());
                }
            }
        }

        return addedCount;
    }

    /**
     * 补充缺失的对赌结算记录
     */
    private int addMissingGamblingRecords() {
        return addMissingGamblingRecords(null);
    }

    /**
     * 补充缺失的对赌结算记录（支持按流水号过滤）
     * @param lsh 对赌流水号，如果为空则处理所有数据
     */
    private int addMissingGamblingRecords(String lsh) {
        int addedCount = 0;

        // 构建查询条件
        QueryWrapper<WlgbDdfq> queryWrapper = new QueryWrapper<WlgbDdfq>()
            .eq("yzzt", 5)  // 已结算
            .eq("sfsc", 0);

        if (lsh != null && !lsh.trim().isEmpty()) {
            queryWrapper.eq("lsh", lsh);
        }

        // 查询所有已结算的对赌
        List<WlgbDdfq> settledGambles = wlgbDdfqService.list(queryWrapper);

        for (WlgbDdfq gamble : settledGambles) {
            // 检查是否已存在对应的公司账户记录
            long existingCount = wlgbDdcompanycountService.count(
                new QueryWrapper<WlgbDdcompanycount>()
                    .eq("lsh", gamble.getLsh())
                    .eq("bz", "对赌结算")
            );

            if (existingCount == 0) {
                // 创建缺失的记录
                WlgbDdcompanycount companyRecord = calculateGamblingCompanyRecord(gamble);
                if (companyRecord != null) {
                    wlgbDdcompanycountService.save(companyRecord);
                    addedCount++;
                    log.info("补充对赌结算记录：流水号={}, 金额={}",
                        gamble.getLsh(), companyRecord.getJsje());
                }
            }
        }

        return addedCount;
    }

    /**
     * 分析公司账户当前状态
     */
    private Map<String, Object> analyzeCompanyAccountStatus() {
        return analyzeCompanyAccountStatus(null);
    }

    /**
     * 分析公司账户当前状态（支持按流水号过滤）
     * @param lsh 对赌流水号，如果为空则分析所有数据
     */
    private Map<String, Object> analyzeCompanyAccountStatus(String lsh) {
        Map<String, Object> status = new HashMap<>();

        QueryWrapper<WlgbDdcompanycount> incomeQuery = new QueryWrapper<WlgbDdcompanycount>().eq("lx", 1);
        QueryWrapper<WlgbDdcompanycount> expenseQuery = new QueryWrapper<WlgbDdcompanycount>().eq("lx", 2);
        QueryWrapper<WlgbDdcompanycount> countQuery = new QueryWrapper<>();

        // 如果指定了流水号，则只分析该流水号的数据
        if (lsh != null && !lsh.trim().isEmpty()) {
            incomeQuery.eq("lsh", lsh);
            expenseQuery.eq("lsh", lsh);
            countQuery.eq("lsh", lsh);
        }

        // 计算总收入
        Double totalIncome = wlgbDdcompanycountService.list(incomeQuery)
            .stream().mapToDouble(WlgbDdcompanycount::getJsje).sum();

        // 计算总支出
        Double totalExpense = wlgbDdcompanycountService.list(expenseQuery)
            .stream().mapToDouble(WlgbDdcompanycount::getJsje).sum();

        // 计算余额
        Double balance = totalIncome - totalExpense;

        status.put("lsh", lsh);
        status.put("totalIncome", totalIncome);
        status.put("totalExpense", totalExpense);
        status.put("balance", balance);
        status.put("totalRecords", wlgbDdcompanycountService.count(countQuery));

        return status;
    }

    /**
     * 删除重复的公司账户记录
     */
    private int removeDuplicateCompanyRecords(String lsh, String bz, Integer lx, String userid) {
        // 查找所有匹配的记录
        List<WlgbDdcompanycount> records = wlgbDdcompanycountService.list(
            new QueryWrapper<WlgbDdcompanycount>()
                .eq("lsh", lsh)
                .eq("bz", bz)
                .eq("lx", lx)
                .eq(userid != null, "userid", userid)
                .orderByAsc("create_time", "id")
        );

        if (records.size() <= 1) {
            return 0; // 没有重复记录
        }

        // 保留第一条记录，删除其余的
        List<String> idsToDelete = records.stream()
            .skip(1) // 跳过第一条
            .map(WlgbDdcompanycount::getId)
            .collect(java.util.stream.Collectors.toList());

        if (!idsToDelete.isEmpty()) {
            wlgbDdcompanycountService.removeByIds(idsToDelete);
            log.info("删除重复记录：lsh={}, bz={}, lx={}, userid={}, 删除数量={}",
                    lsh, bz, lx, userid, idsToDelete.size());
        }

        return idsToDelete.size();
    }

    //平局结算 - 幂等性优化版本
    @RequestMapping("pjjs")
    public Result Pjjs() {
        log.info("开始执行平局结算，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            //线下测试群聊
            String webhook = "";
            String secret = "";
            String jmwebhook = "";
            String jmsecret = "";
            //加盟
            jmwebhook = "https://oapi.dingtalk.com/robot/send?access_token=99b5d2f253edfa919de986f5d71edaf421e5cb9176729918f83485ef942ba3bf";
            jmsecret = "SECf27482b364ba4cbbcdd05c0ef9b8317adf7bbda66aad8868c52ca4415fb05f35";

            if ("prod".equals(hjConfig.getActive())) {
                webhook = "https://oapi.dingtalk.com/robot/send?access_token=cf305260f1f2c912becfafbb8c652c022b09d8842201369e0f410105bae7b763";
                secret = "SEC7ffac84ac3e28bd1e61657cad9d8aeb45290b5c7e05b345cbed815e6aa3e906c";

                // 🔥 关键优化：只获取状态为9的对赌，确保幂等性
                List<WlgbDdfq> list = wlgbDdfqService.list(new QueryWrapper<WlgbDdfq>()
                    .eq("sfsc", 0)
                    .eq("zt", "2")
                    .eq("yzzt", "9"));  // 只处理平局待结算状态的对赌

                if (list.isEmpty()) {
                    log.info("没有需要平局结算的对赌");
                    return Result.OK("没有需要平局结算的对赌");
                }

                int successCount = 0;
                int skipCount = 0;
                List<String> errorMessages = new ArrayList<>();
                for (int ss = 0; ss < list.size(); ss++) {
                    try {
                        WlgbDdfq wlgbDdfq = list.get(ss);
                        String lsh = wlgbDdfq.getLsh();

                        // 🔥 关键：使用CAS确保幂等性，只有状态为9的才能更新为10
                        boolean updated = wlgbDdfqService.update(
                            new WlgbDdfq().setYzzt(10), // 设置为平局已结算状态
                            new QueryWrapper<WlgbDdfq>()
                                .eq("lsh", lsh)
                                .eq("yzzt", 9)  // 只有状态为9的才能更新
                        );

                        if (!updated) {
                            // 如果更新失败，说明已经被处理了
                            skipCount++;
                            log.info("对赌流水号{}已平局结算，跳过", lsh);
                            continue;
                        }

                        List<WlgbDdyz> wlgbDdyzList = wlgbDdyzService.list(new QueryWrapper<WlgbDdyz>().eq("lsh", lsh).groupBy("yzr"));
                        if (wlgbDdyzList != null && wlgbDdyzList.size() > 0) {
                    Map<String, Double> map = new HashMap<>();
                    WlgbDdyz wlgbDdyz = new WlgbDdyz();
                    for (int i = 0; i < wlgbDdyzList.size(); i++) {
                        String yzrid = wlgbDdyzList.get(i).getYzrid();
                        wlgbDdyz = wlgbDdyzService.getOne(new QueryWrapper<WlgbDdyz>().select("sum(yzje) as yzje").eq("lsh", lsh).eq("yzrid", yzrid));
                        if (wlgbDdyz == null) {
                            continue;
                        }
                        Double sum = wlgbDdyz.getYzje();  //押注总金额
                        WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", yzrid));
                        if (wlgbDdryxx == null) {
                            continue;
                        }
                        Double yywlb = 0.0;
                        if (wlgbDdryxx.getWlb() == null) {
                            yywlb = sum;
                        } else {
                            yywlb = wlgbDdryxx.getWlb() + sum;//应有威廉币
                            if (wlgbDdryxx.getSfsc() == 1) {
                                WlgbDdcompanycount wlgbDdcompanycount = new WlgbDdcompanycount();
                                wlgbDdcompanycount.setId(IdConfig.uuId());
                                wlgbDdcompanycount.setZje(0.0);
                                wlgbDdcompanycount.setZcje(0.0);
                                wlgbDdcompanycount.setJsje(sum);
                                wlgbDdcompanycount.setLsh(wlgbDdfq.getLsh());
                                wlgbDdcompanycount.setUserid(wlgbDdryxx.getUserid());
                                wlgbDdcompanycount.setLx(1);
                                wlgbDdcompanycount.setBz("对赌平局");
                                wlgbDdcompanycountService.save(wlgbDdcompanycount);
                                continue;
                            }
                        }
                        wlgbDdryxx.setWlb(yywlb);
                        ddpjgztz(wlgbDdfq, sum, wlgbDdryxx.getUserid());
                        wlgbDdryxxService.update(wlgbDdryxx, new QueryWrapper<WlgbDdryxx>().eq("userid", yzrid));
                        //威廉币记录
                        WlgbDdwlbjl wlgbDdwlbjl = new WlgbDdwlbjl();
                        wlgbDdwlbjl.setJy(1);
                        wlgbDdwlbjl.setJe(sum);
                        wlgbDdwlbjl.setId(IdConfig.uuId());

                        wlgbDdwlbjl.setUserid(wlgbDdryxx.getUserid());
                        wlgbDdwlbjl.setUsername(wlgbDdryxx.getName());
                        wlgbDdwlbjl.setBz("对赌平局");
                        wlgbDdwlbjl.setJyid(wlgbDdfq.getId());
                        wlgbDdwlbjl.setSfsc(1);
                        wlgbDdwlbjlService.save(wlgbDdwlbjl);
                    }
                }
                        //发送群聊
                        List<String> list1 = new ArrayList<>();
                        String content = "[广播][广播] " + wlgbDdfq.getTzfid() + " VS " + wlgbDdfq.getBtzfid() + "的对赌结果为平局！大家押注的威廉币将会原数退回，" +
                                "请前往【企业文化】→【对赌中心】→【威廉币余额】中查看退回威廉币是否到账哦！";
                        if ("团队对赌".equals(wlgbDdfq.getDdlx())) {
                            content = "[广播][广播] (" + wlgbDdfq.getTzfcy() + ") VS (" + wlgbDdfq.getBtzfcy() + ") 的对赌结果为平局！" +
                                    "大家押注的威廉币将会原数退回，请前往【企业文化】→【对赌中心】→【威廉币余额】中查看退回威廉币是否到账哦！";
                        }

                        // 发送钉钉消息（即使失败也不影响状态）
                        try {
                            DingDingUtil.sendMsg(jmwebhook, jmsecret, content, list1, true);
                            DingDingUtil.sendMsg(webhook, secret, content, list1, true);
                        } catch (Exception e) {
                            log.warn("钉钉消息发送失败，对赌流水号：{}, 错误：{}", lsh, e.getMessage());
                        }

                        successCount++;
                        log.info("对赌流水号{}平局结算成功", lsh);

                    } catch (Exception e) {
                        log.error("平局结算失败，流水号：{}, 错误：{}", list.get(ss).getLsh(), e.getMessage(), e);
                        errorMessages.add("流水号" + list.get(ss).getLsh() + "平局结算失败：" + e.getMessage());

                        // 发生异常时回滚状态
                        try {
                            wlgbDdfqService.update(
                                new WlgbDdfq().setYzzt(9), // 回滚到平局待结算状态
                                new QueryWrapper<WlgbDdfq>().eq("lsh", list.get(ss).getLsh())
                            );
                            log.info("对赌流水号{}状态已回滚", list.get(ss).getLsh());
                        } catch (Exception rollbackEx) {
                            log.error("状态回滚失败，流水号：{}", list.get(ss).getLsh(), rollbackEx);
                        }
                    }
                }

                long duration = System.currentTimeMillis() - startTime;
                String message = String.format("平局结算完成！成功：%d，跳过：%d，失败：%d，耗时：%dms",
                        successCount, skipCount, errorMessages.size(), duration);

                if (!errorMessages.isEmpty()) {
                    message += "，失败详情：" + String.join("; ", errorMessages);
                }

                log.info("平局结算执行完成：{}", message);
                return Result.OK(message);
            }

            return Result.OK("非生产环境，跳过执行");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("平局结算执行异常，耗时：{}ms", duration, e);
            return Result.error("平局结算失败：" + e.getMessage());
        }
    }

    //稽查审计待公布池发送工作通知 - 幂等性优化版本
    @RequestMapping("jcDgbcSendTz")
    public Result JcDgbcSendTz() {
        log.info("开始执行稽查审计待公布池发送工作通知，时间：{}", new java.util.Date());
        long startTime = System.currentTimeMillis();

        try {
            String bz = null;
            String formUuid = null;
            if ("dev".equals(hjConfig.getActive())) {
                bz = "办公平台3";
                formUuid = "FORM-56666571GS1VMYA4W3EEOJSCNG8P1VDDN2WVKT";//办公平台3
                return Result.OK("开发环境，跳过执行");
            }

            if ("prod".equals(hjConfig.getActive())) {
                bz = "对赌中心-线上";
                formUuid = "FORM-JD8668C1HHGVAIAZVA8H7MAI6F9Q1Y8RV46WK7";//稽查审计-待公布池

                YDLC ydlc = new YDLC();
                YdAppkey ydAppkey = ydAppkeyService.getOne(
                        new QueryWrapper<YdAppkey>()
                                .lambda()
                                .eq(YdAppkey::getBz, bz)
                );

                if (ydAppkey == null) {
                    log.error("未找到对应的应用配置，bz：{}", bz);
                    return Result.error("未找到对应的应用配置");
                }

                List<Map<String, Object>> list = new ArrayList<>();
                List<JSONObject> list3 = new ArrayList<>();

                String s = ydlc.zzBdSl2("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);

                JSONObject object = JSONObject.parseObject(s);

                int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
                log.info("稽查审计待公布池数量：{}", count);

                if (count > 0) {
                    //发送工作通知模板
                    Dingkey dingkey = dingkeyService.getById("weilianxcx");

                    if (dingkey == null) {
                        log.error("未找到钉钉配置，id：weilianxcx");
                        return Result.error("未找到钉钉配置");
                    }

                    String context = "稽查审计中心有更新了，请赶快点击查看吧！";
                    List<String> list4 = new ArrayList<>();
                    list4.add("16232088869932066");
                    list4.add("15349026426046931");
                    //所有人员id
                    List<String> listry = AllRy();

                    String Url = "https://jztdpp.aliwork.com/APP_XD2KFYUV4YZ1FWN46DEI/custom/FORM-0W9667B1QKHVXUUR16NKGAWSKZDQ2U8AIP8WKD";

                    int successCount = 0;
                    List<String> errorMessages = new ArrayList<>();

                    for (int z = 0; z < listry.size(); z++) {
                        try {
                            DingDBConfig.sendGztz1(listry.get(z), null, dingkey, "稽查审计中心有更新了，请赶快点击查看吧！", context, Url);
                            successCount++;
                        } catch (ApiException e) {
                            log.warn("发送工作通知失败，用户ID：{}, 错误：{}", listry.get(z), e.getMessage());
                            errorMessages.add("用户" + listry.get(z) + "发送失败：" + e.getMessage());
                        }
                    }

                    long duration = System.currentTimeMillis() - startTime;
                    String message = String.format("稽查审计通知发送完成！成功：%d，失败：%d，耗时：%dms",
                            successCount, errorMessages.size(), duration);

                    if (!errorMessages.isEmpty()) {
                        message += "，失败详情：" + String.join("; ", errorMessages);
                    }

                    log.info("稽查审计通知发送完成：{}", message);
                    return Result.OK(message);
                } else {
                    log.info("稽查审计待公布池为空，无需发送通知");
                    return Result.OK("稽查审计待公布池为空，无需发送通知");
                }
            }

            return Result.OK("非生产环境，跳过执行");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("稽查审计通知发送异常，耗时：{}ms", duration, e);
            return Result.error("稽查审计通知发送失败：" + e.getMessage());
        }
    }

    /**
     * 根据给定的ID查询其所有子节点ID
     * 该方法使用广度优先搜索算法，递归查询所有子节点
     *
     * @param bmid 起始节点ID，用于查询其子节点
     * @return 包含起始节点及其所有子节点ID的列表
     */
    public List<String> bmidcx(String bmid) {
        // 存储查询结果的列表
        List<String> result = new ArrayList<>();
        // 使用队列辅助进行广度优先搜索
        Queue<String> queue = new LinkedList<>();
        // 将起始节点ID加入队列和结果列表
        queue.offer(bmid);
        result.add(bmid);
        // 当队列不为空时，继续查询
        while (!queue.isEmpty()) {
            // 从队列中取出当前节点ID
            String currentId = queue.poll();
            // 查询当前节点的所有子节点
            List<String> children = wlgbDdfqService.queryByBm(currentId);
            // 如果子节点列表不为空，则将子节点ID添加到结果列表和队列中
            if (children != null && !children.isEmpty()) {
                for (String child : children) {
                    result.add(child);
                    queue.offer(child);
                }
            }
        }
        // 返回包含所有子节点ID的列表
        return result;
    }

    private void ddpjgztz(WlgbDdfq wlgbDdfq, Double yzje, String userid) {
        WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", userid));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
        Date date = new Date();
        String sj = sdf.format(date);
        Double sy = wlgbDdryxx.getWlb() + yzje;
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);

        String text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "\n\n" +
                "该对赌结果为平局!您本次押注的威廉币将会原数退回！\n\n" +
                "押注金额:" + nf.format(yzje) + "\n\n" +
                "当前剩余威廉币:" + nf.format(sy) + "\n\n" +
                "对赌结算时间:" + sj;
        try {
            DingDBConfig.sendGztzTextByYd(userid, text, "1", "8", "1", "");
        } catch (ApiException e) {
            e.printStackTrace();
        }


    }

    //胜利方团队成员工作通知
    public void slftdgztz(WlgbDdfq wlgbDdfq, WlgbDdtdxx wlgbDdtdxx) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
        Date date = new Date();
        String sj = sdf.format(date);
        String text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "\n\n" +
                "该对赌已结束,作为该团队的成员，您属于胜利方，恭喜您获得胜利！\n\n"
                + "对赌结算时间:" + sj;
        if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
            text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "\n\n" +
                    "该对赌已结束,您作为" + wlgbDdtdxx.getSsfid() + "团队的成员，您属于胜利方，恭喜您获得胜利！\n\n"
                    + "对赌结算时间:" + sj;
        }
        try {
            DingDBConfig.sendGztzTextByYd(wlgbDdtdxx.getUserid(), text, "1", "8", "1", "");
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    //失败方团队成员工作通知
    public void sbftdgztz(WlgbDdfq wlgbDdfq, WlgbDdtdxx wlgbDdtdxx) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
        Date date = new Date();
        String sj = sdf.format(date);
        String text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "\n\n" +
                "该对赌已结束,您作为该团队的成员，您和您的团队未获得本次对赌胜利。不要气馁，请再接再厉！\n\n"
                + "对赌结算时间:" + sj;
        if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
            text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "\n\n" +
                    "该对赌已结束,您作为" + wlgbDdtdxx.getSsfid() + "的成员，您和您的团队未获得本次对赌胜利。不要气馁，请再接再厉！\n\n"
                    + "对赌结算时间:" + sj;
        }
        try {
            DingDBConfig.sendGztzTextByYd(wlgbDdtdxx.getUserid(), text, "1", "8", "1", "");
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    //结算胜利方工作通知
    public void jsslfgztz(String lsh, String userid, Double jsje, int i) {
        WlgbDdfq wlgbDdfq = wlgbDdfqService.getOne(new QueryWrapper<WlgbDdfq>().eq("lsh", lsh));
        if (wlgbDdfq != null) {
            WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", userid));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
            Date date = new Date();
            String sj = sdf.format(date);
            Double sy = wlgbDdryxx.getWlb() + jsje;
            NumberFormat nf = NumberFormat.getInstance();
            nf.setGroupingUsed(false);

            String text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "\n\n" +
                    "该对赌已结束,您押注的一方" + wlgbDdfq.getSlf() + "为胜利方,您本次对赌获得威廉币" + nf.format(jsje) + "个\n\n" +
                    "当前剩余威廉币:" + nf.format(sy) +
                    "\n\n对赌结算时间:" + sj;
            if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                text = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "\n\n" +
                        "该对赌已结束,您押注的一方" + wlgbDdfq.getSlf() + "为胜利方,您本次对赌获得威廉币" + nf.format(jsje) + "个\n\n" +
                        "当前剩余威廉币:" + nf.format(sy) +
                        "\n\n对赌结算时间:" + sj;
            }
            try {
                if (i == 1) {
                    DingDBConfig.sendGztzTextByYd(userid, text, "1", "8", "1", "");
                } else {
                    DingDBConfig.sendGztzTextByYd(userid, text, "1", "8", "1", "");
                }

            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    //结算失败方通知
    public void jssbfgztz(String lsh, String userid, String yzdx) {
        WlgbDdfq wlgbDdfq = wlgbDdfqService.getOne(new QueryWrapper<WlgbDdfq>().eq("lsh", lsh));
        if (wlgbDdfq != null) {
            Double je = 0.0;
            WlgbDdyz wlgbDdyz = wlgbDdyzService.getOne(new QueryWrapper<WlgbDdyz>().select("sum(yzje) as yzje").eq("lsh", wlgbDdfq.getLsh()).eq("yzrid", userid).eq("yzdx", yzdx).groupBy("dxid"));
            if (wlgbDdyz == null) {
                je = 0.0;
            } else {
                je = wlgbDdyz.getYzje();
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
            Date date = new Date();
            String sj = sdf.format(date);
            NumberFormat nf = NumberFormat.getInstance();
            nf.setGroupingUsed(false);
            String text = "对赌:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "\n\n" +
                    "该对赌已结束，很遗憾，您押注的一方" + yzdx + " 本次对赌失败，您未获得任何奖励!\n\n" +
                    "押注失败金额:" + nf.format(je) + "\n\n" +
                    "对赌结算时间:" + sj;
            String text1 = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "\n\n" +
                    "该对赌已结束，很遗憾,您是失败方,不要气馁，请再接再厉！\n\n" +
                    "押注失败金额:" + nf.format(je) + "\n\n" +
                    "对赌结算时间:" + sj;
            if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
                text = "对赌:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "\n\n" +
                        "该对赌已结束，很遗憾，您押注的一方" + yzdx + " 本次对赌失败，您未获得任何奖励!\n\n" +
                        "押注失败金额:" + nf.format(je) + "\n\n" +
                        "对赌结算时间:" + sj;

                text1 = "对赌方:" + wlgbDdfq.getTzfid() + "VS" + wlgbDdfq.getBtzfid() + "VS" + wlgbDdfq.getBtzfid2() + "\n\n" +
                        "该对赌已结束，很遗憾,您是失败方,不要气馁，请再接再厉！\n\n" +
                        "押注失败金额:" + nf.format(je) + "\n\n" +
                        "对赌结算时间:" + sj;
            }
            try {
                if (userid.equals(wlgbDdfq.getTzf()) || userid.equals(wlgbDdfq.getBtzf()) || userid.equals(wlgbDdfq.getBtzf2())) {
                    DingDBConfig.sendGztzTextByYd(userid, text1, "1", "8", "1", "");
                } else {
                    DingDBConfig.sendGztzTextByYd(userid, text, "1", "8", "1", "");
                }
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    //对赌结算后，公司账户总金额进行变化 - 已优化幂等性
    private void jszygszh(WlgbDdfq wlgbDdfq) {
        String lsh = wlgbDdfq.getLsh();
        if (lsh != null) {
            // 🔥 关键：检查是否已存在该流水号的对赌结算记录，避免重复插入
            long existingCount = wlgbDdcompanycountService.count(
                new QueryWrapper<WlgbDdcompanycount>()
                    .eq("lsh", lsh)
                    .eq("bz", "对赌结算")
            );

            if (existingCount > 0) {
                log.info("对赌流水号{}的公司账户记录已存在，跳过重复插入", lsh);
                return;
            }

            //获取公司账户
            WlgbDdcompanycount wlgbDdcompanycount = wlgbDdfqService.queryJsCompany(lsh);
            if (wlgbDdcompanycount == null) {
                log.warn("无法获取对赌流水号{}的公司账户信息", lsh);
                return;
            }

            //设置id
            wlgbDdcompanycount.setId(IdConfig.uuId());
            //获取支出的金额
            Double zcje = wlgbDdcompanycount.getZcje();
            //获取总金额
            Double zje = wlgbDdcompanycount.getZje();

            if (zcje == null || zje == null) {
                log.warn("对赌流水号{}的金额数据异常，支出金额：{}，总金额：{}", lsh, zcje, zje);
                return;
            }

            //结算公司剩余金额
            String je = String.valueOf(zje - zcje);
            int index = je.indexOf(".");
            int i = je.length() - index;
            String intNumber = null;
            if (i < 3) {
                intNumber = je.substring(0, index + i);
            } else {
                intNumber = je.substring(0, index + 3);
            }
            wlgbDdcompanycount.setJsje(Double.valueOf(intNumber));
            wlgbDdcompanycount.setBz("对赌结算");
            wlgbDdcompanycount.setLx(1);
            wlgbDdcompanycount.setCreateTime(new Date());

            wlgbDdcompanycountService.save(wlgbDdcompanycount);
            log.info("成功保存对赌流水号{}的公司账户记录，金额：{}", lsh, wlgbDdcompanycount.getJsje());
        }
    }

    //对赌结算  修改押注级别和威廉币余额 - 已优化幂等性
    public void ddjs(WlgbDdfq wlgbDdfq) {
        String lsh = wlgbDdfq.getLsh();

        // 🔥 关键：检查是否已经结算过，避免重复结算
        long existingRecords = wlgbJsfqjlService.count(new QueryWrapper<WlgbJsfqjl>().eq("lsh", lsh));
        if (existingRecords > 0) {
            log.info("对赌流水号{}已经结算过，跳过重复结算", lsh);
            return;
        }

        Double slpl = 1.0;
        //结算各代表对赌级别
        if (wlgbDdfq.getTzf().equals(wlgbDdfq.getSlfid())) {
            WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdfq.getTzf()));
            if (wlgbDdryxx == null) {
                return;
            }
            if (wlgbDdryxx.getDdjb() == null || wlgbDdryxx.getDdjb() == 0) {
                wlgbDdryxx.setDdjb(1);
            } else {
                wlgbDdryxx.setDdjb(wlgbDdryxx.getDdjb() + 1);
            }
            //修改对赌级别
            wlgbDdryxxService.update(wlgbDdryxx, new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdfq.getTzf()));
            //挑战方赔率
            slpl = wlgbDdfq.getTzfpl();
        } else if (wlgbDdfq.getBtzf().equals(wlgbDdfq.getSlfid())) {
            WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdfq.getBtzf()));
            if (wlgbDdryxx == null) {
                return;
            }
            if (wlgbDdryxx.getDdjb() == null || wlgbDdryxx.getDdjb() == 0) {
                wlgbDdryxx.setDdjb(1);
            } else {
                wlgbDdryxx.setDdjb(wlgbDdryxx.getDdjb() + 1);
            }
            wlgbDdryxxService.update(wlgbDdryxx, new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdfq.getBtzf()));
            slpl = wlgbDdfq.getYzfpl();
        } else if (wlgbDdfq.getBtzf2().equals(wlgbDdfq.getSlfid())) {
            WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdfq.getBtzf2()));
            if (wlgbDdryxx == null) {
                return;
            }
            if (wlgbDdryxx.getDdjb() == null || wlgbDdryxx.getDdjb() == 0) {
                wlgbDdryxx.setDdjb(1);
            } else {
                wlgbDdryxx.setDdjb(wlgbDdryxx.getDdjb() + 1);
            }
            wlgbDdryxxService.update(wlgbDdryxx, new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdfq.getBtzf2()));
            slpl = wlgbDdfq.getYzfpl2();
        }
        // 成员对赌级别
        if ("团队对赌".equals(wlgbDdfq.getDdlx())) {
            List<WlgbDdtdxx> listdxx = wlgbDdtdxxService.list(new QueryWrapper<WlgbDdtdxx>().eq("ssfid", wlgbDdfq.getSlfid()).eq("lsh", lsh));
            for (int j = 0; j < listdxx.size(); j++) {
                WlgbDdryxx wlgbDdryxx1 = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", listdxx.get(j).getUserid()));
                //胜利方不是记录表里面的胜利的代表人则等级+1
                if (!wlgbDdfq.getSlfid().equals(wlgbDdryxx1.getUserid())) {
                    wlgbDdryxx1.setDdjb(wlgbDdryxx1.getDdjb() + 1);
                    wlgbDdryxxService.update(wlgbDdryxx1, new QueryWrapper<WlgbDdryxx>().eq("userid", listdxx.get(j).getUserid()));
                    //胜利方团队成员发送工作通知
                    slftdgztz(wlgbDdfq, listdxx.get(j));
                }
            }
            List<WlgbDdtdxx> list = wlgbDdtdxxService.list(new QueryWrapper<WlgbDdtdxx>().eq("lsh", lsh).ne("ssfid", wlgbDdfq.getSlfid()));
            for (int z = 0; z < list.size(); z++) {
                WlgbDdryxx wlgbDdryxx1 = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", list.get(z).getUserid()));
                if (!wlgbDdfq.getTzf().equals(wlgbDdryxx1.getUserid()) && !wlgbDdfq.getBtzf().equals(wlgbDdryxx1.getUserid())) {
                    // 失败方团队成员发送工作通知
                    sbftdgztz(wlgbDdfq, list.get(z));
                }
            }
        }
        if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
            List<WlgbDdtdxx> listdxx = wlgbDdtdxxService.list(new QueryWrapper<WlgbDdtdxx>().eq("ssfid", wlgbDdfq.getSlfid()).eq("lsh", lsh));
            for (int j = 0; j < listdxx.size(); j++) {
                WlgbDdryxx wlgbDdryxx1 = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", listdxx.get(j).getUserid()));
                //胜利方不是记录表里面的胜利的代表人则等级+1
                if (!wlgbDdfq.getSlfid().equals(wlgbDdryxx1.getUserid())) {
                    wlgbDdryxx1.setDdjb(wlgbDdryxx1.getDdjb() + 1);
                    wlgbDdryxxService.update(wlgbDdryxx1, new QueryWrapper<WlgbDdryxx>().eq("userid", listdxx.get(j).getUserid()));
                    //胜利方团队成员发送工作通知
                    slftdgztz(wlgbDdfq, listdxx.get(j));
                }
            }
            List<WlgbDdtdxx> list = wlgbDdtdxxService.list(new QueryWrapper<WlgbDdtdxx>().eq("lsh", lsh).ne("ssfid", wlgbDdfq.getSlfid()));
            for (int z = 0; z < list.size(); z++) {
                WlgbDdryxx wlgbDdryxx1 = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", list.get(z).getUserid()));
                if (!wlgbDdfq.getTzf().equals(wlgbDdryxx1.getUserid()) && !wlgbDdfq.getBtzf().equals(wlgbDdryxx1.getUserid()) && !wlgbDdfq.getBtzf2().equals(wlgbDdryxx1.getUserid())) {
                    // 失败方团队成员发送工作通知
                    sbftdgztz(wlgbDdfq, list.get(z));
                }
            }
        }
        //结算押注级别
        WlgbDdfq wlgbDdfq1 = wlgbDdfqService.getOne(new QueryWrapper<WlgbDdfq>().eq("lsh", lsh));
        if (wlgbDdfq1 == null) {
            return;
        }
        String slf = wlgbDdfq1.getSlf();
        List<WlgbDdyz> list2 = wlgbDdyzService.list(new QueryWrapper<WlgbDdyz>().eq("lsh", lsh).eq("yzdx", slf).groupBy("yzrid"));
        if (list2.size() == 0) {
            return;
        }
        //修改押注人的压住等级（只有赢的人需要修改，输的人等级不变），在这个之前bug还不是很多
        for (WlgbDdyz wlgbDdyz : list2) {
            String userid1 = wlgbDdyz.getYzrid();
            WlgbDdryxx wlgbDdryxx2 = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", userid1));
            if (wlgbDdryxx2.getYzjb() == null || wlgbDdryxx2.getYzjb() == 0) {
                wlgbDdryxx2.setYzjb(1);
            } else {
                wlgbDdryxx2.setYzjb(wlgbDdryxx2.getYzjb() + 1);
            }
            wlgbDdryxxService.update(wlgbDdryxx2, new QueryWrapper<WlgbDdryxx>().eq("userid", userid1));
        }
        //结算威廉币,这是所有的押注记录数据
        List<WlgbDdyz> list = wlgbDdfqService.selectSumYzje(lsh);
        for (WlgbDdyz wlgbDdyz : list) {
            WlgbDdwlbjl wlgbDdwlbjl = new WlgbDdwlbjl();
            WlgbDdryxx wlgbDdryxx = wlgbDdryxxService.getOne(new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdyz.getYzrid()));
            //所有押注的人中判断谁押注的人是正好是胜利方
            if (wlgbDdyz.getYzdx().equals(wlgbDdfq.getSlf())) {
                Double wlb = wlgbDdyz.getYzje() * slpl;
                String k = String.format("%.3f", wlb);
                int l = k.indexOf(".");
                k = k.substring(0, l + 3);
                Double jswlb = new Double(k);
                //原来的威廉币
                Double ylwlb = wlgbDdryxx.getWlb() == null ? 0.0 : wlgbDdryxx.getWlb();
                //修改威廉币  ==之前剩余的威廉币+本次结算的威廉币
                wlgbDdryxx.setWlb(ylwlb + jswlb);
                //威廉币记录
                wlgbDdwlbjl.setId(IdConfig.uuId());
                wlgbDdwlbjl.setJe(jswlb);
                wlgbDdwlbjl.setBz("对赌结算");
                wlgbDdwlbjl.setJy(1);
                wlgbDdwlbjl.setUserid(wlgbDdryxx.getUserid());
                wlgbDdwlbjl.setUsername(wlgbDdryxx.getName());
                wlgbDdwlbjl.setJyid(lsh);
                wlgbDdwlbjl.setSfsc(0);
                //结算发钱记录
                WlgbJsfqjl wlgbJsfqjl = new WlgbJsfqjl();
                wlgbJsfqjl.setId(UUID.randomUUID().toString().replace("-", ""));
                wlgbJsfqjl.setLsh(lsh);
                wlgbJsfqjl.setSlyqr(wlgbDdryxx.getName());
                wlgbJsfqjl.setSlyqrid(wlgbDdryxx.getUserid());
                wlgbJsfqjl.setJe(jswlb);
                wlgbJsfqjl.setZt(1);
                wlgbJsfqjlService.save(wlgbJsfqjl);
                wlgbDdwlbjlService.save(wlgbDdwlbjl);
                if (wlgbDdryxx.getSfsc() == 1) {
                    //结算离职人员的威廉币，结算到公司账户
                    // 🔥 关键：检查是否已存在该用户的离职人员结算记录，避免重复插入
                    long existingCount = wlgbDdcompanycountService.count(
                        new QueryWrapper<WlgbDdcompanycount>()
                            .eq("lsh", lsh)
                            .eq("userid", wlgbDdryxx.getUserid())
                            .eq("bz", "离职人员结算")
                    );

                    if (existingCount == 0) {
                        WlgbDdcompanycount wlgbDdcompanycount = new WlgbDdcompanycount();
                        wlgbDdcompanycount.setId(IdConfig.uuId());
                        wlgbDdcompanycount.setJsje(jswlb);
                        wlgbDdcompanycount.setZcje(0.0);
                        wlgbDdcompanycount.setZje(0.0);
                        wlgbDdcompanycount.setBz("离职人员结算");
                        wlgbDdcompanycount.setLsh(lsh);
                        wlgbDdcompanycount.setUserid(wlgbDdryxx.getUserid());
                        wlgbDdcompanycount.setLx(1);
                        wlgbDdcompanycount.setCreateTime(new Date());
                        wlgbDdcompanycountService.save(wlgbDdcompanycount);
                        log.info("保存离职人员结算记录：流水号={}, 用户={}, 金额={}", lsh, wlgbDdryxx.getUserid(), jswlb);
                    } else {
                        log.info("离职人员结算记录已存在，跳过：流水号={}, 用户={}", lsh, wlgbDdryxx.getUserid());
                    }
                    continue;
                }
                int z = 0;
                WlgbDdfq wlgbDdfq3 = wlgbDdfqService.getOne(new QueryWrapper<WlgbDdfq>().eq("lsh", lsh));
                if (wlgbDdyz.getYzrid().equals(wlgbDdfq3.getSlfid())) {
                    z = 1;
                }
                //结算胜利方工作通知
                jsslfgztz(lsh, wlgbDdryxx.getUserid(), jswlb, z);
                //这是修改
                wlgbDdryxxService.update(wlgbDdryxx, new QueryWrapper<WlgbDdryxx>().eq("userid", wlgbDdyz.getYzrid()));
            } else {
                WlgbJsfqjl wlgbJsfqjl = new WlgbJsfqjl();
                wlgbJsfqjl.setId(UUID.randomUUID().toString().replace("-", ""));
                wlgbJsfqjl.setLsh(lsh);
                wlgbJsfqjl.setSlyqr(wlgbDdryxx.getName());
                wlgbJsfqjl.setSlyqrid(wlgbDdryxx.getUserid());
                wlgbJsfqjl.setJe(0.0);
                wlgbJsfqjl.setZt(0);
                wlgbJsfqjlService.save(wlgbJsfqjl);
                //结算失败方工作通知
                jssbfgztz(lsh, wlgbDdryxx.getUserid(), wlgbDdyz.getYzdx());
            }
        }
        //对赌结算后，公司账户总金额进行变化
        jszygszh(wlgbDdfq);
    }

    public List AllRy() {
        //获取要发送的人员名单
        //黑马项目组
        List list = bmidcx("81870208");
        //        外部投资人
        List list1 = bmidcx("33502412");

        //待复工成员
        List list2 = bmidcx("401821558");
        //长沙赢氏大户人家酒店管理有限公司
        List list3 = bmidcx("860197171");
        //所有不发工作通知的部门id
        List<String> listAll = new ArrayList();
        listAll.addAll(list);
        listAll.addAll(list1);
        listAll.addAll(list2);
        listAll.addAll(list3);
        List<String> listry = new ArrayList();
        List<DingdingEmployee> dingdingEmployeeList = dingdingEmployeeService.list();

        //去掉不发工作通知的部门
        for (int z = 0; z < dingdingEmployeeList.size(); z++) {
            int s = 0;
            for (int i = 0; i < listAll.size(); i++) {
                if (dingdingEmployeeList.get(z).getDepartid().contains(listAll.get(i))) {
                    s = 1;
                    break;
                }
            }
            if (s == 0) {
                listry.add(dingdingEmployeeList.get(z).getUserid());
            }
        }

        return listry;
    }

    //选择截取
    public String xzjq(String userid) {
        if (userid.contains("\"")) {
            String s = userid.substring(0, 1);
            if ("\"".equals(s)) {
                userid = userid.substring(1, userid.length() - 1);

            } else {
                userid = null;
            }
        }

        return userid;
    }

    public String xzjq1(String userid) {
        if (userid.contains("[")) {
            String s = userid.substring(0, 1);
            if ("[".equals(s)) {
                userid = userid.substring(1, userid.length() - 1);

            } else {
                userid = null;
            }
        }

        return userid;
    }

    //生成押注表格
    public String yzbg(List<Map<String, Object>> list, WlgbDdfq wlgbDdfq) throws Exception {
        Map<String, Object> map = new HashMap<>();
        WlgbDdyz wlgbDdyz = wlgbDdyzService.getOne(new QueryWrapper<WlgbDdyz>().select("sum(yzje) as yzje").eq("lsh", wlgbDdfq.getLsh()).eq("yzdx", wlgbDdfq.getTzfid()));
        if (wlgbDdyz == null) {
            return "数据不存在！";
        }
        Double tzfzje = wlgbDdyz.getYzje();
        WlgbDdyz wlgbDdyz1 = wlgbDdyzService.getOne(new QueryWrapper<WlgbDdyz>().select("sum(yzje) as yzje").eq("lsh", wlgbDdfq.getLsh()).eq("yzdx", wlgbDdfq.getBtzfid()));
        if (wlgbDdyz1 == null) {
            return "数据不存在";
        }

        Double yzfzje = wlgbDdyz1.getYzje();
        SimpleDateFormat sp = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date date = new Date();
        String a = sp.format(wlgbDdfq.getYzjzsj());
        String ddlx = wlgbDdfq.getDdlx();
        Double yzfyzje2 = 0.0;
        if ("多人对赌".equals(ddlx)) {
            WlgbDdyz wlgbDdyz2 = wlgbDdyzService.getOne(new QueryWrapper<WlgbDdyz>().select("sum(yzje) as yzje").eq("lsh", wlgbDdfq.getLsh()).eq("yzdx", wlgbDdfq.getBtzfid2()));
            if (wlgbDdyz2 == null) {
                return "数据不存在";
            }
            yzfyzje2 = wlgbDdyz2.getYzje();
        }
        String str = null;
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        List<Map<String, Object>> list1 = new ArrayList<>();
        List<Map<String, Object>> list2 = new ArrayList<>();
        List<Map<String, Object>> list3 = new ArrayList<>();


        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).get("yzdx").equals(wlgbDdfq.getTzfid())) {
                list1.add(list.get(i));
            } else if (list.get(i).get("yzdx").equals(wlgbDdfq.getBtzfid())) {
                list2.add(list.get(i));
            } else if (list.get(i).get("yzdx").equals(wlgbDdfq.getBtzfid2())) {
                list3.add(list.get(i));
            }
        }
        Integer y = 500 + (list.size() * 25);

        if (ddlx == "个人对赌" || "个人对赌".equals(ddlx)) {
            str = "<table>" +
                    "<tr style='height: 40px'><th width='20%' colspan='8'style='font-size:35px'>个人对赌协议</th></tr>" +
                    "<tr><td width='20%' colspan='8' style='text-align: left'>一、对赌方:" + wlgbDdfq.getTzfid() + "vs" + wlgbDdfq.getBtzfid() + "<br/>" +
                    "二、对赌内容:" + wlgbDdfq.getDdnr() + "<br/>" +
                    "三、赌注:" + wlgbDdfq.getDz() + "<br/>" +
                    "四、押注截止时间:" + a + "<br/>" +
                    "</td>" +
                    "</tr>" +
                    "<tr style='height: 40px'>" + "<th width='20%' colspan='8' style=\"background-color: red; font-size:25px\">" + "如果当月出现弄虚作假的，直接判输！" + "</th>" + "</tr>" +
                    "<tr>" + "<th width='20%' colspan='4' style=\"background-color: #4682B4;\" height='5%'>" + "押注-" + wlgbDdfq.getTzfid() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #48D1CC;\" >" + "押注-" + wlgbDdfq.getBtzfid() + "</th>" +
                    "</tr>" +
                    "<tr>" + "<th width='20%' colspan='4' style=\"background-color: #4682B4;\" height='5%'>" + "赔率" + "1：" + wlgbDdfq.getTzfpl() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #48D1CC;\" >" + "赔率" + "1：" + wlgbDdfq.getYzfpl() + "</th>" +
                    "</tr>" +
                    "<tr style='height: 40px'>" +
                    "                    <th width='40%' >序号</th>" +
                    "                    <th width='60%' >部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='60%' '>押注金额</th>" +
                    "                    <th width='40%' >序号</th>" +
                    "                    <th width='60%'>部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='60%' >押注金额</th>" +
                    "                </tr>" +
                    "<tr style='height: 40px'>" + "<th width='20%' colspan='4' style=\"background-color: #4682B4;\">" + "合计：" + nf.format(tzfzje) + "</th>" +
                    "<th width='20%' colspan='4' style=\"background-color: #48D1CC;\">" + "合计：" + nf.format(yzfzje) + "</th>" +
                    "</tr>";
        } else if (ddlx == "团队对赌" || "团队对赌".equals(ddlx)) {//团队对赌
            str = "<table>" +
                    "<tr style='height: 40px'><th width='20%' colspan='8'style='font-size:35px'>" + "团队对赌协议" + "</th></tr>" +
                    "<tr>" + "<td width='20%' colspan='8' style='text-align: left'>" + "一、对赌方:(" + wlgbDdfq.getTzfcy() + ")vs(" + wlgbDdfq.getBtzfcy() + ")<br/>" +
                    "二、对赌内容:" + wlgbDdfq.getDdnr() + "<br/>" +
                    "三、赌注:" + wlgbDdfq.getDz() + "<br/>" +
                    "四、押注截止时间:" + a + "<br/>" +
                    "</td>" +
                    "</tr>" +
                    "<tr style='height: 40px'>" + "<th width='20%' colspan='8' style=\"background-color: red; font-size:25px\">" + "如果当月出现弄虚作假的，直接判输！" + "</th>" + "</tr>" +
                    "<tr>" + "<th width='20%' colspan='4' style=\"background-color: #4682B4;\" height='5%'>" + "押注-" + wlgbDdfq.getTzfcy() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #48D1CC;\" >" + "押注-" + wlgbDdfq.getBtzfcy() + "</th>" +
                    "</tr>" +
                    "<tr>" + "<th width='20%' colspan='4' style=\"background-color: #4682B4;\" height='5%'>" + "赔率" + "1：" + wlgbDdfq.getTzfpl() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #48D1CC;\" >" + "赔率" + "1：" + wlgbDdfq.getYzfpl() + "</th>" +
                    "</tr>" +
                    "<tr style='height: 40px'>" +
                    "                    <th width='40%' >序号</th>" +
                    "                    <th width='60%' >部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='60%' '>押注金额</th>" +
                    "                    <th width='40%' >序号</th>" +
                    "                    <th width='60%'>部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='60%' >押注金额</th>" +
                    "                </tr>" +
                    "<tr style='height: 40px'>" + "<th width='20%' colspan='4' style=\"background-color: #4682B4;\">" + "合计：" + nf.format(tzfzje) + "</th>" +
                    "<th width='20%' colspan='4' style=\"background-color: #48D1CC;\">" + "合计：" + nf.format(yzfzje) + "</th>" +
                    "</tr>";
        } else if (ddlx == "多人对赌" || "多人对赌".equals(ddlx)) {//多人对赌

            str = "<table>" +
                    "<tr style='height: 40px'><th width='20%' colspan='12'style='font-size:35px'>" + "团队对赌协议" + "</th></tr>" +
                    "<tr>" +
                    "<td width='20%' colspan='12' style='text-align: left'>" +
                    "一、对赌方:(" + wlgbDdfq.getTzfcy() + ")vs(" + wlgbDdfq.getBtzfcy() + ")vs(" + wlgbDdfq.getBtzfcy2() + ")<br/>" +
                    "二、对赌内容:" + wlgbDdfq.getDdnr() + "<br/>" +
                    "三、赌注:" + wlgbDdfq.getDz() + "<br/>" +
                    "四、押注截止时间:" + a + "<br/>" +
                    "</td>" +
                    "</tr>" +
                    "<tr style='height: 40px'>" +
                    "<th width='20%' colspan='12' style=\"background-color: red; font-size:25px\">" +
                    "如果当月出现弄虚作假的，直接判输！" + "</th>" +
                    "</tr>" +
                    "<tr>" +
                    "<th width='20%' colspan='4' style=\"background-color: #4682B4;\" height='5%'>" + "押注-" + wlgbDdfq.getTzfcy() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #48D1CC;\" >" + "押注-" + wlgbDdfq.getBtzfcy() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #BFD6AA;\" >" + "押注-" + wlgbDdfq.getBtzfcy2() + "</th>" +
                    "</tr>" +
                    "<tr>" +
                    "<th width='20%' colspan='4' style=\"background-color: #4682B4;\" height='5%'>" + "赔率" + "1：" + wlgbDdfq.getTzfpl() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #48D1CC;\" >" + "赔率" + "1：" + wlgbDdfq.getYzfpl() + "</th>" +
                    "<th width='20%' colspan='4' height='5%' style=\"background-color: #BFD6AA;\" >" + "赔率" + "1：" + wlgbDdfq.getYzfpl2() + "</th>" +
                    "</tr>" +
                    "<tr style='height: 40px'>" +
                    "                    <th width='20%' >序号</th>" +
                    "                    <th width='50%' >部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='40%' '>押注金额</th>" +
                    "                    <th width='20%' >序号</th>" +
                    "                    <th width='50%'>部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='40%' >押注金额</th>" +
                    "                    <th width='20%' >序号</th>" +
                    "                    <th width='50%'>部门</th>" +
                    "                    <th width='30%' >姓名</th>" +
                    "                    <th width='40%' >押注金额</th>" +
                    "                </tr>" +
                    "<tr style='height: 40px'>" +
                    "<th width='20%' colspan='4' style=\"background-color: #4682B4;\">" + "合计：" + nf.format(tzfzje) + "</th>" +
                    "<th width='20%' colspan='4' style=\"background-color: #48D1CC;\">" + "合计：" + nf.format(yzfzje) + "</th>" +
                    "<th width='20%' colspan='4' style=\"background-color: #BFD6AA;\">" + "合计：" + nf.format(yzfyzje2) + "</th>" +
                    "</tr>";
        }


        if ("多人对赌".equals(wlgbDdfq.getDdlx())) {
            //list1>list2>list3
            if (list1.size() >= list2.size() && list2.size() >= list3.size()) {
                for (int j = 0; j < list1.size(); j++) {
                    if (j < list3.size()) {

                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else if (j < list2.size() && j > list3.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "</tr>";
                    } else {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "</tr>";
                    }
                }
                y = 500 + list1.size() * 33;
                //list1>list3>list2
            } else if (list1.size() > list3.size() && list3.size() > list2.size()) {
                for (int j = 0; j < list1.size(); j++) {
                    if (j < list2.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color:#BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else if (j >= list2.size() && j < list3.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "</tr>";
                    }
                }
                y = 500 + list1.size() * 33;
                //list2>list3>list1
            } else if (list2.size() > list3.size() && list3.size() > list1.size()) {
                for (int j = 0; j < list2.size(); j++) {
                    if (j < list1.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else if (j >= list1.size() && j < list3.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else {
                        str += "<tr style='height: 40px'>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "</tr>";
                    }
                }
                y = 500 + list2.size() * 33;
                //list3>list2>list1
            } else if (list3.size() > list2.size() && list2.size() > list1.size()) {
                for (int j = 0; j < list3.size(); j++) {
                    if (j < list1.size()) {

                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else if (j < list2.size() && j >= list1.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else {
                        str += "<tr style='height: 40px'>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    }
                }
                y = 500 + list3.size() * 33;
                //list3>list1>list2
            } else if (list3.size() > list1.size() && list1.size() > list2.size()) {
                for (int j = 0; j < list3.size(); j++) {
                    if (j < list2.size()) {

                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else if (j < list1.size() && j >= list2.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else {
                        str += "<tr style='height: 40px'>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    }
                }
                y = 500 + list3.size() * 33;
                //list2>list1>list3
            } else if (list2.size() > list1.size() && list1.size() > list3.size()) {
                for (int j = 0; j < list2.size(); j++) {
                    if (j < list3.size()) {

                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #BFD6AA;\">" + (j + 1) + "</td>";
                        str += "<td>" + list3.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list3.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list3.get(j).get("grzje")) + "</td>";

                        str += "</tr>";
                    } else if (j < list1.size() && j >= list3.size()) {
                        str += "<tr style='height: 40px'>";

                        str += "<td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "</tr>";
                    } else {
                        str += "<tr style='height: 40px'>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>";


                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";

                        str += "</tr>";
                    }
                }
                y = 500 + list2.size() * 33;
            }


            str += "<tr style='height: 40px'>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "</tr>";
            str += "<tr style='height: 40px'>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "</tr>";

        } else {
            if (list1.size() > list2.size()) {
                for (int j = 0; j < list1.size(); j++) {
                    if (j < list2.size()) {

                        str += "<tr style='height: 40px'><td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>" + "</tr>";
                    } else {
                        str += "<tr style='height: 40px'><td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";

                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>" + "</tr>";
                    }
                }
            } else {
                for (int j = 0; j < list2.size(); j++) {
                    if (j < list1.size()) {
                        str += "<tr style='height: 40px'><td style=\"background-color: #4682B4;\">" + (j + 1) + "</td>";
                        str += "<td>" + list1.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list1.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list1.get(j).get("grzje")) + "</td>";
                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>" + "</tr>";
                    } else {
                        str += "<tr style='height: 40px'><td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td></td>";
                        str += "<td style=\"background-color: #48D1CC;\">" + (j + 1) + "</td>";
                        str += "<td>" + list2.get(j).get("dqmz") + "</td>";
                        str += "<td>" + list2.get(j).get("yzr") + "</td>";
                        str += "<td>" + nf.format(list2.get(j).get("grzje")) + "</td>" + "</tr>";
                    }
                }
            }
            str += "<tr style='height: 40px'>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "</tr>";
            str += "<tr style='height: 40px'>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "<td></td><td></td><td></td><td></td>" +
                    "</tr>";
        }


        str += "</table>";
        map.put("str", str);
        String tempName = PathUtil.getFileAbsolutePath2("static" + File.separator + "pyqbbry.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = UUID.randomUUID().toString().replace("-", "");
        String pdf = PathUtil.getFileAbsolutePath("static" + File.separator, id + ".pdf");
        String png = PathUtil.getFileAbsolutePath("static" + File.separator, id + ".png");
        File newPdf = new File(pdf);
        File newPng = new File(png);
        //生成pdf
        PDFUtil.createPdf1(context, newPdf.getPath(), 1000, y);
        //生成图片
        PDFUtil.pdfToImg(newPdf.getPath(), 1, newPng.getPath());
        FileInputStream fileInputStream = new FileInputStream(newPng);
        MultipartFile multipartFile = new MockMultipartFile(newPng.getName(), fileInputStream);
        fileInputStream.close();
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        newPdf.delete();
        newPng.delete();
        return upload;
    }

}


